{"data_mtime": 1754307504, "dep_lines": [7, 7, 9, 6, 8, 9, 10, 11, 222, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 10, 10, 20, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "torch.nn", "matplotlib.pyplot", "torch", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sys", "os", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "typing"], "hash": "a2a9b90fef3424b00e0ddd8c3ecd9c06b8352cad", "id": "debug_pa_loss", "ignore_all": false, "interface_hash": "0e6577b58a640b15b2b272e287a6bfbde62b7881", "mtime": 1754307496, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\debug_pa_loss.py", "plugin_data": null, "size": 7429, "suppressed": [], "version_id": "1.15.0"}