{"data_mtime": 1754302183, "dep_lines": [8, 7, 6, 9, 10, 11, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.nn.functional", "torch.nn", "torch", "numpy", "typing", "copy", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._tensor"], "hash": "582ab7a9e7169d222a4ab37da2838e20017ef1bd", "id": "utils.forgetting_scheduler", "ignore_all": true, "interface_hash": "6793f8c202c05d432f2daa09eff04c30384dec4c", "mtime": 1754292364, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\utils\\forgetting_scheduler.py", "plugin_data": null, "size": 25533, "suppressed": ["sklearn.metrics"], "version_id": "1.15.0"}