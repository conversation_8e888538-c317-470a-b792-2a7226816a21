{"data_mtime": 1754302339, "dep_lines": [5, 9, 10, 13, 1, 3, 4, 6, 7, 8, 13, 22, 129, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 20, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["utils.utils", "utils.setup_elements", "models.temp_model", "utils.name_match", "<PERSON><PERSON><PERSON><PERSON>", "numpy", "torch", "os", "traceback", "temp_de", "utils", "gc", "datetime", "builtins", "_frozen_importlib", "_typeshed", "abc", "agents", "agents.base", "models", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "torch._C", "torch._tensor", "torch.backends", "torch.backends.cudnn", "torch.cuda", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.types", "types", "typing", "typing_extensions"], "hash": "6da1ca1de8b145e1f7e31d58e91d188a05905236", "id": "general_main", "ignore_all": false, "interface_hash": "f057288e4f9e19b03dfd2763f8ffaf3a488ad5eb", "mtime": 1754308939, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\general_main.py", "plugin_data": null, "size": 42740, "suppressed": [], "version_id": "1.15.0"}