"""
内存优化配置
用于控制遗忘过程中的内存使用
"""

import torch


class MemoryConfig:
    """内存优化配置类"""
    
    def __init__(self, gpu_memory_gb: float = 12.0):
        """
        初始化内存配置
        
        Args:
            gpu_memory_gb: GPU内存大小（GB）
        """
        self.gpu_memory_gb = gpu_memory_gb
        
        # 根据GPU内存大小自动调整批处理大小
        if gpu_memory_gb >= 24:
            # 高端GPU (RTX 4090, A100等)
            self.eval_batch_size = 64
            self.unlearn_batch_size = 32
            self.mia_batch_size = 32
        elif gpu_memory_gb >= 16:
            # 中高端GPU (RTX 3080Ti, RTX 4080等)
            self.eval_batch_size = 48
            self.unlearn_batch_size = 24
            self.mia_batch_size = 24
        elif gpu_memory_gb >= 12:
            # 中端GPU (RTX 3060Ti, RTX 4070等)
            self.eval_batch_size = 32
            self.unlearn_batch_size = 16
            self.mia_batch_size = 16
        elif gpu_memory_gb >= 8:
            # 入门级GPU (RTX 3060, GTX 1080等)
            self.eval_batch_size = 24
            self.unlearn_batch_size = 12
            self.mia_batch_size = 12
        else:
            # 低端GPU或集成显卡
            self.eval_batch_size = 16
            self.unlearn_batch_size = 8
            self.mia_batch_size = 8
        
        # 内存清理设置
        self.clear_cache_frequency = 5  # 每5个批次清理一次缓存
        self.enable_memory_monitoring = True
        
        print(f"内存配置初始化完成 (GPU: {gpu_memory_gb}GB)")
        print(f"  评估批处理大小: {self.eval_batch_size}")
        print(f"  遗忘训练批处理大小: {self.unlearn_batch_size}")
        print(f"  MIA批处理大小: {self.mia_batch_size}")
    
    @staticmethod
    def get_gpu_memory_gb():
        """获取当前GPU的内存大小（GB）"""
        if torch.cuda.is_available():
            return torch.cuda.get_device_properties(0).total_memory / 1024**3
        return 0.0
    
    @classmethod
    def auto_config(cls):
        """自动配置内存设置"""
        gpu_memory = cls.get_gpu_memory_gb()
        return cls(gpu_memory)
    
    def print_memory_usage(self):
        """打印当前内存使用情况"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            total = torch.cuda.get_device_properties(0).total_memory / 1024**3
            
            print(f"GPU内存使用情况:")
            print(f"  已分配: {allocated:.2f}GB / {total:.2f}GB ({allocated/total*100:.1f}%)")
            print(f"  已保留: {reserved:.2f}GB / {total:.2f}GB ({reserved/total*100:.1f}%)")
            print(f"  可用: {total-reserved:.2f}GB")
    
    def clear_memory(self):
        """清理GPU内存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            import gc
            gc.collect()
    
    def check_memory_pressure(self, threshold: float = 0.85):
        """
        检查内存压力
        
        Args:
            threshold: 内存使用阈值（0-1）
            
        Returns:
            bool: 是否存在内存压力
        """
        if not torch.cuda.is_available():
            return False
        
        total = torch.cuda.get_device_properties(0).total_memory
        reserved = torch.cuda.memory_reserved()
        
        usage_ratio = reserved / total
        return usage_ratio > threshold
    
    def adaptive_batch_size(self, base_batch_size: int, data_size: int):
        """
        自适应批处理大小
        
        Args:
            base_batch_size: 基础批处理大小
            data_size: 数据总大小
            
        Returns:
            int: 调整后的批处理大小
        """
        # 如果内存压力大，减小批处理大小
        if self.check_memory_pressure(0.8):
            adjusted_size = max(base_batch_size // 2, 4)
            print(f"检测到内存压力，批处理大小从 {base_batch_size} 调整为 {adjusted_size}")
            return adjusted_size
        
        # 如果数据量很小，可以使用更大的批处理大小
        if data_size < base_batch_size:
            return data_size
        
        return base_batch_size


# 全局内存配置实例
MEMORY_CONFIG = MemoryConfig.auto_config()


def get_memory_config():
    """获取全局内存配置"""
    return MEMORY_CONFIG


def update_forgetting_scheduler_config(scheduler):
    """更新遗忘调度器的内存配置"""
    config = get_memory_config()
    
    # 为遗忘调度器添加内存配置属性
    scheduler.memory_config = config
    scheduler.eval_batch_size = config.eval_batch_size
    scheduler.unlearn_batch_size = config.unlearn_batch_size
    scheduler.mia_batch_size = config.mia_batch_size
    
    print("遗忘调度器内存配置已更新")


# 内存优化装饰器
def memory_optimized(clear_cache=True):
    """
    内存优化装饰器
    
    Args:
        clear_cache: 是否在函数执行后清理缓存
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 执行前清理内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 执行后清理内存
                if clear_cache and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    import gc
                    gc.collect()
        
        return wrapper
    return decorator


# 批处理处理器
class BatchProcessor:
    """批处理处理器，用于安全地处理大批量数据"""
    
    def __init__(self, batch_size: int = None):
        self.config = get_memory_config()
        self.batch_size = batch_size or self.config.eval_batch_size
    
    def process_batches(self, data, process_func, **kwargs):
        """
        分批处理数据
        
        Args:
            data: 输入数据（tensor或tuple of tensors）
            process_func: 处理函数
            **kwargs: 传递给处理函数的额外参数
            
        Returns:
            处理结果列表
        """
        if isinstance(data, torch.Tensor):
            data_size = data.size(0)
            data_list = [data]
        elif isinstance(data, (list, tuple)):
            data_size = data[0].size(0)
            data_list = list(data)
        else:
            raise ValueError("不支持的数据类型")
        
        # 自适应批处理大小
        batch_size = self.config.adaptive_batch_size(self.batch_size, data_size)
        
        results = []
        for i in range(0, data_size, batch_size):
            # 准备批次数据
            if len(data_list) == 1:
                batch_data = data_list[0][i:i+batch_size]
            else:
                batch_data = tuple(d[i:i+batch_size] for d in data_list)
            
            # 处理批次
            batch_result = process_func(batch_data, **kwargs)
            results.append(batch_result)
            
            # 定期清理内存
            if (i // batch_size) % self.config.clear_cache_frequency == 0:
                self.config.clear_memory()
        
        return results
