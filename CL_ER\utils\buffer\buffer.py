from utils.setup_elements import input_size_match
# 移除直接导入，改为延迟导入
# from utils import name_match #import update_methods, retrieve_methods
from utils.utils import maybe_cuda
import torch
import torch.nn as nn
from utils.buffer.buffer_utils import BufferClassTracker
from utils.setup_elements import n_classes

class Buffer(nn.Module):
    """
    修改缓冲区类以支持统一接口
    """
    def __init__(self, model_encoder, model_classifier, params):
        super(Buffer, self).__init__()  # 初始化nn.Module父类
        
        # 处理两种可能的情况
        self.model_encoder = None
        self.model_classifier = None
        self.model = None  # 添加model属性以兼容ASER更新方法
        
        if model_encoder is not None and model_classifier is not None:
            # 分离的编码器和分类器
            self.model_encoder = model_encoder
            self.model_classifier = model_classifier
            # 兼容ASER，为model属性赋值
            self.model = model_encoder  # ASER主要使用特征提取器
        elif model_encoder is not None:
            # 单一模型，尝试提取组件
            self.model = model_encoder
            if hasattr(self.model, 'extract_components'):
                self.model_encoder, self.model_classifier = self.model.extract_components()
        
        self.params = params
        self.cuda = self.params.cuda
        self.current_index = 0
        self.n_seen_so_far = 0
        self.device = "cuda" if self.params.cuda else "cpu"

        # 检查并设置ASER所需的参数
        if params.update == 'ASER' or params.retrieve == 'ASER':
            self._ensure_aser_params(params)

        # define buffer
        buffer_size = params.mem_size
        print('buffer has %d slots' % buffer_size)
        input_size = input_size_match[params.data]
        buffer_img = maybe_cuda(torch.FloatTensor(buffer_size, *input_size).fill_(0))
        buffer_label = maybe_cuda(torch.LongTensor(buffer_size).fill_(0))
        # 添加任务ID张量
        tasks = maybe_cuda(torch.LongTensor(buffer_size).fill_(-1))

        # registering as buffer allows us to save the object using `torch.save`
        self.register_buffer('buffer_img', buffer_img)
        self.register_buffer('buffer_label', buffer_label)
        self.register_buffer('tasks', tasks)  # 注册任务ID张量

        # 延迟导入name_match以避免循环导入
        from utils import name_match
        # define update and retrieve method
        self.update_method = name_match.update_methods[params.update](params)
        self.retrieve_method = name_match.retrieve_methods[params.retrieve](params)

        if self.params.buffer_tracker:
            self.buffer_tracker = BufferClassTracker(n_classes[params.data], self.device)

    def _ensure_aser_params(self, params):
        """确保ASER所需的参数都已设置"""
        # 设置ASER必要参数的默认值
        if not hasattr(params, 'k') or params.k is None:
            print("警告: 未设置k参数，使用默认值5")
            params.k = 5
            
        if not hasattr(params, 'n_smp_cls') or params.n_smp_cls is None:
            print("警告: 未设置n_smp_cls参数，使用默认值2.0")
            params.n_smp_cls = 2.0
            
        if not hasattr(params, 'aser_type'):
            print("警告: 未设置aser_type参数，使用默认值'asvm'")
            params.aser_type = 'asvm'
            
        # 检查是否设置了num_tasks和out_dim
        if not hasattr(params, 'num_tasks'):
            print("警告: 未设置num_tasks参数，使用默认值5")
            params.num_tasks = 5
            
        # 确保输出维度参数存在（类别数量）
        if hasattr(params, 'data') and params.data in n_classes:
            out_dim = n_classes[params.data]
            print(f"数据集 {params.data} 的类别数量: {out_dim}")
        else:
            print("警告: 未找到数据集的类别数量信息，使用默认值3")
            out_dim = 3
            
        return params

    def update(self, x, y,**kwargs):
        """更新缓冲区并返回更新的索引
        
        Args:
            x: 输入数据
            y: 标签
            **kwargs: 其他参数
            
        Returns:
            updated_indices: 更新的样本索引列表
        """
        return self.update_method.update(buffer=self, x=x, y=y, **kwargs)


    def retrieve(self, **kwargs):
        # 检查是否有遗忘任务需要过滤
        if hasattr(self, 'forgotten_tasks') and self.forgotten_tasks:
            return self._retrieve_with_forgetting_filter(**kwargs)
        return self.retrieve_method.retrieve(buffer=self, **kwargs)

    def _retrieve_with_forgetting_filter(self, **kwargs):
        """
        检索数据时过滤掉遗忘任务的数据
        """
        # 获取原始检索结果
        original_result = self.retrieve_method.retrieve(buffer=self, **kwargs)

        if len(original_result) != 2:
            return original_result

        x, y = original_result
        if x.size(0) == 0:
            return x, y

        # 获取对应的任务ID
        if hasattr(self, 'tasks') and self.current_index > 0:
            # 找到这些样本对应的任务ID
            valid_indices = []
            for i in range(min(x.size(0), self.current_index)):
                task_id = self.tasks[i].item()
                if task_id not in self.forgotten_tasks:
                    valid_indices.append(i)

            if len(valid_indices) == 0:
                # 所有样本都被过滤掉了
                empty_x = torch.empty(0, *x.shape[1:], device=x.device, dtype=x.dtype)
                empty_y = torch.empty(0, device=y.device, dtype=y.dtype)
                return empty_x, empty_y

            # 只返回未被遗忘的任务数据
            valid_indices = torch.tensor(valid_indices, device=x.device)
            filtered_x = x[valid_indices]
            filtered_y = y[valid_indices]

            return filtered_x, filtered_y

        return x, y

    def set_forgotten_tasks(self, forgotten_tasks: set):
        """
        设置已遗忘的任务集合

        Args:
            forgotten_tasks: 已遗忘的任务ID集合
        """
        self.forgotten_tasks = forgotten_tasks

    def get_all_data(self):
        """
        获取缓冲区中的所有数据

        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: (数据, 标签, 任务ID)
        """
        if self.current_index == 0:
            # 缓冲区为空
            empty_tensor = torch.empty(0, *self.buffer_img.shape[1:])
            empty_labels = torch.empty(0, dtype=torch.long)
            empty_tasks = torch.empty(0, dtype=torch.long)
            if self.cuda:
                empty_tensor = empty_tensor.cuda()
                empty_labels = empty_labels.cuda()
                empty_tasks = empty_tasks.cuda()
            return empty_tensor, empty_labels, empty_tasks

        # 返回当前有效的数据
        valid_data = self.buffer_img[:self.current_index]
        valid_labels = self.buffer_label[:self.current_index]
        valid_tasks = self.tasks[:self.current_index]

        return valid_data, valid_labels, valid_tasks

    def get_task_data(self, task_id):
        """
        获取指定任务的数据

        Args:
            task_id: 任务ID

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (数据, 标签)
        """
        if self.current_index == 0:
            # 缓冲区为空
            empty_tensor = torch.empty(0, *self.buffer_img.shape[1:])
            empty_labels = torch.empty(0, dtype=torch.long)
            if self.cuda:
                empty_tensor = empty_tensor.cuda()
                empty_labels = empty_labels.cuda()
            return empty_tensor, empty_labels

        # 找到指定任务的数据
        task_mask = self.tasks[:self.current_index] == task_id
        task_data = self.buffer_img[:self.current_index][task_mask]
        task_labels = self.buffer_label[:self.current_index][task_mask]

        return task_data, task_labels

    def remove_task_data(self, task_id):
        """
        从缓冲区中移除指定任务的数据

        Args:
            task_id: 要移除的任务ID
        """
        if self.current_index == 0:
            return

        # 找到不是指定任务的数据
        keep_mask = self.tasks[:self.current_index] != task_id
        keep_indices = torch.where(keep_mask)[0]

        if len(keep_indices) == 0:
            # 所有数据都被移除
            self.current_index = 0
            return

        # 重新排列数据
        self.buffer_img[:len(keep_indices)] = self.buffer_img[keep_indices]
        self.buffer_label[:len(keep_indices)] = self.buffer_label[keep_indices]
        self.tasks[:len(keep_indices)] = self.tasks[keep_indices]

        # 更新索引
        self.current_index = len(keep_indices)

        print(f"已从缓冲区移除任务 {task_id} 的数据，剩余 {self.current_index} 个样本")

    def analyze_buffer_distribution(self):
        """
        分析缓冲区中各任务的样本分布
        """
        if self.current_index == 0:
            print("缓冲区为空")
            return

        valid_tasks = self.tasks[:self.current_index]
        unique_tasks, counts = torch.unique(valid_tasks, return_counts=True)

        print(f"\n缓冲区样本分布 (总计 {self.current_index} 个样本):")
        for task_id, count in zip(unique_tasks.cpu().numpy(), counts.cpu().numpy()):
            percentage = count / self.current_index * 100
            print(f"  任务 {task_id}: {count} 个样本 ({percentage:.1f}%)")

    def optimize_buffer_for_current_task(self, current_task_id, max_current_ratio=0.5, min_task_ratio=0.05):
        """
        优化缓冲区分配，确保当前任务有最多样本，历史任务保持合理比例

        Args:
            current_task_id: 当前任务ID
            max_current_ratio: 当前任务的最大样本比例
            min_task_ratio: 每个历史任务的最小样本比例
        """
        if self.current_index == 0:
            return

        valid_tasks = self.tasks[:self.current_index]
        unique_tasks, counts = torch.unique(valid_tasks, return_counts=True)

        # 计算理想的样本分配
        total_capacity = self.buffer_img.size(0)
        current_task_samples = min(int(total_capacity * max_current_ratio),
                                 counts[unique_tasks == current_task_id].sum().item() if current_task_id in unique_tasks else 0)

        # 为历史任务分配剩余空间
        remaining_capacity = total_capacity - current_task_samples
        historical_tasks = unique_tasks[unique_tasks != current_task_id]

        if len(historical_tasks) > 0:
            min_samples_per_task = max(1, int(total_capacity * min_task_ratio))
            samples_per_historical_task = max(min_samples_per_task,
                                            remaining_capacity // len(historical_tasks))

        # 重新组织缓冲区（这里可以实现更复杂的重新分配逻辑）
        print(f"缓冲区优化完成 - 当前任务 {current_task_id} 目标样本数: {current_task_samples}")
        if len(historical_tasks) > 0:
            print(f"历史任务平均样本数: {samples_per_historical_task}")