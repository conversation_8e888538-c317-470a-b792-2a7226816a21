{"data_mtime": 1751962523, "dep_lines": [2, 4, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "utils.buffer.buffer_utils", "torch.nn", "utils.utils", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._tensor", "torch.return_types", "torch.types", "typing"], "hash": "85a3e663344a38b152b1c0bbf76fdb5949a7e71c", "id": "utils.buffer.gss_greedy_update", "ignore_all": false, "interface_hash": "439cbee1641430b8679b053f163e82cb0525ddbf", "mtime": 1754303477, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\utils\\buffer\\gss_greedy_update.py", "plugin_data": null, "size": 6616, "suppressed": [], "version_id": "1.15.0"}