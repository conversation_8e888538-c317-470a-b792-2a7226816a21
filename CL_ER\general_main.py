import argparse
# import random
import numpy as np
import torch
from utils.utils import boolean_string
import os
import traceback
from temp_de import continual_learning_EEG_run
from utils.setup_elements import setup_architecture
from models.temp_model import ClassificationHead
from utils.utils import maybe_cuda
from utils.setup_elements import setup_opt
import utils.name_match as name_match
# 注释掉旧的遗忘代理导入，现在使用集成在temp_de.py中的遗忘调度器
# from agents.compare_forgetting import CompareForgetAgent
# from agents.adversarial import BasicUnlearningAgent
# from agents.advanced_unlearning import EnhancedUnlearningAgent
# from agents.selective_surgical import SelectiveSurgicalAgent
# from agents.noise_injection import NoiseInjectionAgent
# from agents.dual_teacher import DualTeacherAgent
# from agents.hybrid import HybridUnlearningAgent
import gc

# 设置内存优化选项
def setup_memory_optimization(args):
    """设置内存优化选项"""
    # 清理内存
    gc.collect()
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        
        if args.optimize_memory:
            # 设置PyTorch内存分配策略，减少内存碎片
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = f'max_split_size_mb:{args.max_split_size_mb}'
            
            # 限制GPU内存使用
            if args.gpu_memory_fraction < 1.0:
                torch.cuda.set_per_process_memory_fraction(args.gpu_memory_fraction)
                
            print(f"已启用内存优化: 最大分割大小={args.max_split_size_mb}MB, GPU内存使用比例={args.gpu_memory_fraction}")
            
            # 设置确定性计算，可能会降低速度但减少内存使用
            if args.deterministic:
                torch.backends.cudnn.deterministic = True
                torch.backends.cudnn.benchmark = False
                print("已启用确定性计算")

os.environ['CUDA_VISIBLE_DEVICES']='0'
def main(args):
    print(args)
    
    # 设置内存优化
    setup_memory_optimization(args)
    
    # # set up seed
    # np.random.seed(args.seed)
    # random.seed(args.seed)
    # torch.manual_seed(args.seed)
    # if args.cuda:
    #     torch.cuda.manual_seed(args.seed)
    #     torch.backends.cudnn.deterministic = True
    #     torch.backends.cudnn.benchmark = False
    args.trick = {'labels_trick': args.labels_trick, 'separated_softmax': args.separated_softmax,
                  'kd_trick': args.kd_trick, 'kd_trick_star': args.kd_trick_star, 'review_trick': args.review_trick,
                  'ncm_trick': args.ncm_trick}
    
    # 内存优化：降低精度以减少内存使用
    if args.cuda and args.half_precision:
        print("启用半精度训练以节省内存")
        torch.set_default_tensor_type(torch.HalfTensor)
    
    # 根据运行模式执行不同的操作
    # 注意：遗忘功能现在已经集成到temp_de.py的持续学习主循环中
    # 使用 --forget_task_ids 参数即可在训练过程中自动执行遗忘

    if args.mode == 'train':  # 移除 'train_unlearn' 模式，现在统一使用 'train'
        # 训练模式 - 遗忘功能已集成到持续学习流程中
        agent, acc = run_training(args)

        # 注释掉旧的遗忘逻辑，现在在temp_de.py中处理
        # if args.mode == 'train_unlearn' and args.forget_task_id is not None:
        #     run_unlearning(args, agent, acc)

    # 注释掉独立的遗忘模式，现在遗忘集成在训练过程中
    # elif args.mode == 'unlearn':
    #     # 仅遗忘模式，从保存的模型加载并执行遗忘
    #     if not args.load_model_path:
    #         print("错误: 遗忘模式需要指定已保存的模型路径 (--load_model_path)")
    #         return
    #
    #     agent, acc = load_model(args)
    #     if agent is not None:
    #         run_unlearning(args, agent, acc)

    elif args.mode == 'eval':
        # 仅评估模式，从保存的模型加载并评估
        if not args.load_model_path:
            print("错误: 评估模式需要指定已保存的模型路径 (--load_model_path)")
            return

        agent, acc = load_model(args)
        if agent is not None:
            # 这里可以添加更多的评估代码
            print(f"模型评估准确率: {acc:.4f}")

    else:
        print("错误: 不支持的运行模式")
        print("支持的模式:")
        print("  --mode train: 训练模式（支持集成遗忘，使用 --forget_task_ids 参数）")
        print("  --mode eval: 评估模式")
        print("")
        print("遗忘使用示例:")
        print("  python general_main.py --mode train --forget_task_ids 1 3 5")
        return
    
    # 清理内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

def run_training(args):
    """执行持续学习训练过程"""
    agent, acc = continual_learning_EEG_run(args, store=args.store, save_path=args.save_path)
    
    # 保存训练完成后的模型，为后续遗忘做准备
    if args.store and args.save_path:
        # 获取当前时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 构建保存路径，包含代理名称、更新方法、检索方法和时间戳
        model_save_path = os.path.join(
            args.save_path, 
            f"{args.agent}_{args.update}_{args.retrieve}_{timestamp}_trained_model.pth"
        )
        
        # 根据模型结构保存
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            # 分离的编码器和分类器
            save_dict = {
                'model_encoder_state_dict': agent.model_encoder.state_dict(),
                'model_cls_state_dict': agent.model_cls.state_dict() if hasattr(agent, 'model_cls') and agent.model_cls is not None else None,
                'buffer_state_dict': agent.buffer.state_dict() if hasattr(agent.buffer, 'state_dict') else None,
                'args': args,
                'accuracy': acc,
                'timestamp': timestamp,
                'agent': args.agent,
                'update': args.update,
                'retrieve': args.retrieve
            }
        else:
            # 单一模型
            save_dict = {
                'model_state_dict': agent.model.state_dict(),
                'buffer_state_dict': agent.buffer.state_dict() if hasattr(agent.buffer, 'state_dict') else None,
                'args': args,
                'accuracy': acc,
                'timestamp': timestamp,
                'agent': args.agent,
                'update': args.update,
                'retrieve': args.retrieve
            }
        
        # 保存任务总样本数信息
        if hasattr(agent, 'task_total_samples'):
            save_dict['task_total_samples'] = agent.task_total_samples
        
        torch.save(save_dict, model_save_path)
        print(f"持续学习训练完成的模型已保存至: {model_save_path}")
        
        # 同时保存一个最新版本的文件（方便后续加载）
        latest_model_path = os.path.join(args.save_path, f"latest_trained_model.pth")
        torch.save(save_dict, latest_model_path)
        print(f"最新训练模型已保存到 {latest_model_path}")
    
    return agent, acc

def load_model(args):
    """从保存的模型加载"""
    print(f"正在从保存的模型加载: {args.load_model_path}")
    try:
        # 检查是否是新格式的模型文件
        is_new_format = ('_model.pth' in args.load_model_path or 
                         '_trained_model.pth' in args.load_model_path or 
                         '_unlearned_model.pth' in args.load_model_path)
        
        # 检查是否是旧的PAER格式的模型文件
        is_paer_format = 'paer_model' in args.load_model_path
        
        # 确定缓冲区路径
        buffer_path = None
        if is_new_format:
            # 新格式的缓冲区路径
            if '_model.pth' in args.load_model_path:
                buffer_path = args.load_model_path.replace('_model.pth', '_buffer.pth')
            else:
                # 尝试查找同一目录下的最新缓冲区文件
                buffer_dir = os.path.dirname(args.load_model_path)
                if os.path.exists(os.path.join(buffer_dir, 'latest_buffer.pth')):
                    buffer_path = os.path.join(buffer_dir, 'latest_buffer.pth')
                    print(f"将使用最新的缓冲区文件: {buffer_path}")
        elif is_paer_format:
            # 旧的PAER格式缓冲区路径
            buffer_path = args.load_model_path.replace('paer_model', 'paer_buffer')
        
        if buffer_path:
            print(f"将尝试加载对应的缓冲区: {buffer_path}")
        
        # 创建模型组件
        model_encoder = setup_architecture(args)
        if model_encoder is None:
            print("错误：model_encoder未成功创建，请检查setup_architecture函数")
            return None, 0
        model_encoder = maybe_cuda(model_encoder, args.cuda)
        model_cls = ClassificationHead(64, 3)
        if model_cls is None:
            print("错误：model_cls未成功创建，请检查ClassificationHead构造函数")
            return None, 0
        model_cls = maybe_cuda(model_cls, args.cuda)
        
        print(f"模型创建成功 - model_encoder类型: {type(model_encoder)}, model_cls类型: {type(model_cls)}")
        
        # 创建优化器
        opt = setup_opt(args.optimizer, model_encoder, model_cls, args.learning_rate, args.weight_decay)
        
        # 加载模型
        model_state = torch.load(args.load_model_path)
        
        # 根据不同的模型格式加载状态
        if is_new_format or is_paer_format:
            # 新格式或PAER格式
            if 'encoder' in model_state:
                model_encoder.load_state_dict(model_state['encoder'])
                print("成功加载编码器状态")
            elif 'model_encoder_state_dict' in model_state:
                model_encoder.load_state_dict(model_state['model_encoder_state_dict'])
                print("成功加载编码器状态")
            
            if 'classifier' in model_state:
                model_cls.load_state_dict(model_state['classifier'])
                print("成功加载分类器状态")
            elif 'model_cls_state_dict' in model_state and model_state['model_cls_state_dict'] is not None:
                model_cls.load_state_dict(model_state['model_cls_state_dict'])
                print("成功加载分类器状态")
                
            if 'optimizer' in model_state:
                opt.load_state_dict(model_state['optimizer'])
                print("成功加载优化器状态")
        else:
            # 旧格式
            if 'model_encoder_state_dict' in model_state and model_encoder is not None:
                model_encoder.load_state_dict(model_state['model_encoder_state_dict'])
                if 'model_cls_state_dict' in model_state and model_state['model_cls_state_dict'] is not None and model_cls is not None:
                    model_cls.load_state_dict(model_state['model_cls_state_dict'])
                print("成功加载编码器和分类器状态")
            elif 'model_state_dict' in model_state:
                print("警告: 保存的是单一模型状态，但当前使用分离的编码器和分类器")
                try:
                    if model_encoder is not None:
                        model_encoder.load_state_dict(model_state['model_state_dict'])
                        print("已尝试将模型状态加载到编码器")
                    else:
                        print("警告: model_encoder为None，无法加载模型状态")
                except Exception as load_err:
                    print(f"无法将模型状态加载到编码器: {load_err}")
        
        # 创建代理
        agent_class = name_match.agents[args.agent]
        agent = agent_class((model_encoder, model_cls), opt, args)
        
        # 加载任务总样本数信息
        if 'task_total_samples' in model_state and hasattr(agent, 'task_total_samples'):
            agent.task_total_samples = model_state['task_total_samples']
            print("成功加载任务总样本数信息")
        
        # 如果有缓冲区路径，加载缓冲区
        if buffer_path and os.path.exists(buffer_path):
            buffer_state = torch.load(buffer_path)
            
            if hasattr(agent.buffer, 'buffer_img'):
                agent.buffer.buffer_img.copy_(buffer_state['buffer_img'])
            if hasattr(agent.buffer, 'buffer_label'):
                agent.buffer.buffer_label.copy_(buffer_state['buffer_label'])
            if hasattr(agent.buffer, 'current_index'):
                agent.buffer.current_index = buffer_state['current_index']
            if hasattr(agent.buffer, 'n_seen_so_far'):
                agent.buffer.n_seen_so_far = buffer_state['n_seen_so_far']
            if hasattr(agent.buffer, 'tasks') and 'tasks' in buffer_state:
                agent.buffer.tasks.copy_(buffer_state['tasks'])
                
            print(f"成功加载缓冲区，当前包含 {agent.buffer.current_index} 个样本")
            
            # 分析缓冲区分布
            if hasattr(agent, 'analyze_buffer_distribution'):
                print("\n分析加载后的缓冲区分布:")
                agent.analyze_buffer_distribution()
        else:
            print(f"警告: 未找到缓冲区文件 {buffer_path}")
        
        # 获取准确率
        acc = 0
        if 'accuracy' in model_state:
            if isinstance(model_state['accuracy'], list):
                # 如果是准确率列表，计算平均值
                acc_list = model_state['accuracy']
                acc = np.mean([np.mean(a) for a in acc_list]) if acc_list else 0
            else:
                acc = model_state['accuracy']
        
        # 打印模型信息
        print(f"模型加载成功，准确率: {acc:.4f}")
        if 'timestamp' in model_state:
            print(f"模型保存时间: {model_state['timestamp']}")
        if 'agent' in model_state:
            print(f"代理类型: {model_state['agent']}")
        if 'update' in model_state:
            print(f"更新方法: {model_state['update']}")
        if 'retrieve' in model_state:
            print(f"检索方法: {model_state['retrieve']}")
        if 'unlearn_mode' in model_state:
            print(f"遗忘模式: {model_state['unlearn_mode']}")
        if 'forget_task_id' in model_state:
            print(f"遗忘任务ID: {model_state['forget_task_id']}")
        
        return agent, acc
    except Exception as e:
        print(f"加载模型时出错: {e}")
        traceback.print_exc()
        return None, 0

# 注释掉旧的遗忘函数，遗忘功能现在已经集成到temp_de.py的持续学习主循环中
# 使用 --forget_task_ids 参数即可在训练过程中自动执行遗忘

# def run_unlearning(args, agent, acc):
#     """执行遗忘操作 - 已废弃，现在使用集成在temp_de.py中的遗忘调度器"""
#     print(f"\n======= 开始主动遗忘任务 {args.forget_task_id} =======")
#     print(f"遗忘模式: {args.unlearn_mode}")
#
#     # 将args.unlearn_mode映射到args.unlearning_mode以保持兼容性
#     args.unlearning_mode = args.unlearn_mode
#
#     # 检查是否存在对应的缓冲区文件
#     buffer_path = os.path.join(args.save_path, f"paer_buffer_task2.pth")
#     if os.path.exists(buffer_path):
#         print(f"发现单独保存的缓冲区文件: {buffer_path}")
#         try:
#             # 加载缓冲区
#             buffer_state = torch.load(buffer_path)
#             if hasattr(agent.buffer, 'buffer_img'):
#                 agent.buffer.buffer_img.copy_(buffer_state['buffer_img'])
#             if hasattr(agent.buffer, 'buffer_label'):
#                 agent.buffer.buffer_label.copy_(buffer_state['buffer_label'])
#             if hasattr(agent.buffer, 'current_index'):
#                 agent.buffer.current_index = buffer_state['current_index']
#             if hasattr(agent.buffer, 'n_seen_so_far'):
#                 agent.buffer.n_seen_so_far = buffer_state['n_seen_so_far']
#             if hasattr(agent.buffer, 'tasks') and 'tasks' in buffer_state:
#                 agent.buffer.tasks.copy_(buffer_state['tasks'])
#
#             print(f"成功加载缓冲区，当前包含 {agent.buffer.current_index} 个样本")
#         except Exception as e:
#             print(f"加载缓冲区时出错: {e}")
#
#     # 根据指定的模式选择不同的遗忘方法
#     if args.unlearn_mode == 'adversarial':
#         unlearning_agent = BasicUnlearningAgent(agent.model, agent.opt, args)
#     elif args.unlearn_mode == 'enhanced':
#         unlearning_agent = EnhancedUnlearningAgent(agent.model, agent.opt, args)
#     elif args.unlearn_mode == 'surgical':
#         unlearning_agent = SelectiveSurgicalAgent(agent.model, agent.opt, args)
#     elif args.unlearn_mode == 'dual_teacher':
#         unlearning_agent = DualTeacherAgent(agent.model, agent.opt, args)
#     elif args.unlearn_mode == 'noise':
#         unlearning_agent = NoiseInjectionAgent(agent.model, agent.opt, args)
#     elif args.unlearn_mode == 'hybrid':
#         unlearning_agent = HybridUnlearningAgent(agent.model, agent.opt, args)
#     elif args.unlearn_mode == 'selective':
#         unlearning_agent = SelectiveSurgicalAgent(agent.model, agent.opt, args)
#     else:
#         print(f"错误：不支持的遗忘模式 '{args.unlearn_mode}'")
#         return
#
#     # 复制缓冲区和测试数据
#     unlearning_agent.buffer = agent.buffer
#
#     # 执行遗忘操作 - 已废弃，现在在temp_de.py中处理
#     try:
#         # 直接调用forget方法
#         results = unlearning_agent.forget(args.forget_task_id)
#
#         # 检查结果是否包含必要的键
#         if 'task_id' not in results:
#             print("警告: 遗忘结果中没有task_id信息")
#             results['task_id'] = args.forget_task_id
#
#         if 'forget_samples' not in results:
#             print("警告: 遗忘结果中没有forget_samples信息")
#             results['forget_samples'] = 0
#
#         if 'retain_samples' not in results:
#             print("警告: 遗忘结果中没有retain_samples信息")
#             results['retain_samples'] = 0
#
#         # 打印遗忘结果
#         print("\n====== 遗忘任务评估结果 ======")
#         print(f"遗忘任务ID: {results['task_id']}")
#         print(f"遗忘样本数: {results['forget_samples']}")
#         print(f"保留样本数: {results['retain_samples']}")
#
#         # 优先显示测试集评估结果
#         if 'pre_forget_accuracy_test' in results:
#             print("\n----- 测试集评估结果 -----")
#             print(f"遗忘前准确率 - 遗忘任务: {results['pre_forget_accuracy_test']:.4f}, 保留任务: {results['pre_retain_accuracy_test']:.4f}")
#             print(f"遗忘后准确率 - 遗忘任务: {results['post_forget_accuracy_test']:.4f}, 保留任务: {results['post_retain_accuracy_test']:.4f}")
#             print(f"准确率变化 - 遗忘任务: {results['forget_accuracy_change_test']:.4f}, 保留任务: {results['retain_accuracy_change_test']:.4f}")
#         else:
#             print("\n----- 缓冲区评估结果 -----")
#             pre_forget = results.get('pre_forget_accuracy', results.get('pre_forget_accuracy_buffer', 0.0))
#             post_forget = results.get('post_forget_accuracy', results.get('post_forget_accuracy_buffer', 0.0))
#             pre_retain = results.get('pre_retain_accuracy', results.get('pre_retain_accuracy_buffer', 0.0))
#             post_retain = results.get('post_retain_accuracy', results.get('post_retain_accuracy_buffer', 0.0))
#
#             print(f"遗忘前准确率: {pre_forget:.4f}")
#             print(f"遗忘后准确率: {post_forget:.4f}")
#             print(f"准确率变化: {post_forget - pre_forget:.4f}")
#
#         if 'mia_accuracy' in results:
#             print(f"\n----- 成员推理攻击评估 -----")
#             print(f"MIA攻击准确率: {results['mia_accuracy']:.4f} (随机基线: 0.5)")
#             print(f"MIA攻击AUC: {results['mia_auc']:.4f}")
#             print(f"MIA评估遗忘成功: {results['forget_success_mia']}")
#
#         if 'forget_success_acc' in results:
#             print(f"\n----- 遗忘成功评估 -----")
#             print(f"准确率评估遗忘成功: {results['forget_success_acc']}")
#
#         if 'forget_success_overall' in results:
#             print(f"整体遗忘成功: {results['forget_success_overall']}")
#
#         # 如果保存路径存在，则保存遗忘后的模型
#         if args.store and args.save_path:
#             # 获取当前时间戳
#             import datetime
#             timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
#
#             # 构建保存路径，包含代理名称、遗忘模式、任务ID和时间戳
#             model_save_path = os.path.join(
#                 args.save_path,
#                 f"{args.agent}_{args.unlearn_mode}_task{args.forget_task_id}_{timestamp}_unlearned_model.pth"
#             )
#
#             # 保存模型
#             save_dict = {
#                 'model_encoder_state_dict': unlearning_agent.model_encoder.state_dict(),
#                 'model_cls_state_dict': unlearning_agent.model_cls.state_dict() if unlearning_agent.model_cls is not None else None,
#                 'buffer_state_dict': unlearning_agent.buffer.state_dict() if hasattr(unlearning_agent.buffer, 'state_dict') else None,
#                 'unlearning_results': results,
#                 'args': args,
#                 'timestamp': timestamp,
#                 'agent': args.agent,
#                 'unlearn_mode': args.unlearn_mode,
#                 'forget_task_id': args.forget_task_id
#             }
#
#             # 保存任务总样本数信息
#             if hasattr(unlearning_agent, 'task_total_samples'):
#                 save_dict['task_total_samples'] = unlearning_agent.task_total_samples
#
#             torch.save(save_dict, model_save_path)
#             print(f"遗忘后的模型已保存至: {model_save_path}")
#
#             # 同时保存一个最新版本的遗忘模型（方便后续加载）
#             latest_unlearned_path = os.path.join(args.save_path, f"latest_unlearned_model.pth")
#             torch.save(save_dict, latest_unlearned_path)
#             print(f"最新遗忘模型已保存到 {latest_unlearned_path}")
#
#     except Exception as e:
#         print(f"执行遗忘操作时出错: {e}")
#         traceback.print_exc()


if __name__ == "__main__":
    # Commandline arguments
    parser = argparse.ArgumentParser(description="Online Continual Learning PyTorch")
    ########################General#########################
    parser.add_argument('--num_runs', dest='num_runs', default=1, type=int,
                        help='Number of runs (default: %(default)s)')
    parser.add_argument('--seed', dest='seed', default=0, type=int,
                        help='Random seed')
    parser.add_argument('--Net', dest='Net', default='temp_model',
                        choices=['resnet', 'eccn', 'model', 'temp_model', 'eccn_transformer', 'SCWT'],
                        help='Net selection  (default: %(default)s)')

    ########################Misc#########################
    parser.add_argument('--val_size', dest='val_size', default=0.1, type=float,
                        help='val_size (default: %(default)s)')
    parser.add_argument('--num_val', dest='num_val', default=3, type=int,
                        help='Number of batches used for validation (default: %(default)s)')
    parser.add_argument('--num_runs_val', dest='num_runs_val', default=3, type=int,
                        help='Number of runs for validation (default: %(default)s)')
    parser.add_argument('--error_analysis', dest='error_analysis', default=False, type=boolean_string,
                        help='Perform error analysis (default: %(default)s)')
    parser.add_argument('--verbose', type=boolean_string, default=True,
                        help='print information or not (default: %(default)s)')
    parser.add_argument('--store', type=boolean_string, default=True,
                        help='Store result or not (default: %(default)s)')
    parser.add_argument('--save-path', dest='save_path', default='./output/models',
                        help='Path to save models and results (default: %(default)s)')

    ########################Agent#########################
    parser.add_argument('--agent', dest='agent', default='PAER',
                        choices=['ER', 'EWC', 'AGEM', 'CNDPM', 'LWF', 'ICARL', 'GDUMB', 'ASER', 'SCR', 'PAER', 'CPL_ER', 'PAFL_ER', 'APUMM_ER', 'PAMU_ER'],
                        help='Agent selection  (default: %(default)s)')
    parser.add_argument('--update', dest='update', default='random', choices=['random', 'GSS', 'ASER'],
                        help='Update method  (default: %(default)s)')
    parser.add_argument('--retrieve', dest='retrieve', default='random', choices=['MIR', 'random', 'ASER', 'match', 'mem_match'],
                        help='Retrieve method  (default: %(default)s)')
    parser.add_argument('--prototype_weight', dest='prototype_weight', default=1.0, type=float,
                        help='原型锚定损失权重，用于PAER代理 (default: %(default)s)')
    
    ########################运行模式#########################
    parser.add_argument('--mode', dest='mode', default='train',
                        choices=['train', 'eval'],
                        help='运行模式: train(训练，支持集成遗忘), eval(仅评估) (default: %(default)s)')

    ########################遗忘参数（集成在训练过程中）#########################
    parser.add_argument('--forget_task_id', dest='forget_task_id', default=None, type=int,
                        help='要遗忘的单个任务ID，兼容旧版本参数 (default: %(default)s)')
    parser.add_argument('--forget_task_ids', dest='forget_task_ids', nargs='+', type=int, default=None,
                        help='要遗忘的任务ID列表，支持多个任务遗忘，例如: --forget_task_ids 1 3 5 (default: %(default)s)')
    parser.add_argument('--unlearning_epochs', dest='unlearning_epochs', default=20, type=int,
                        help='遗忘训练轮数 (default: %(default)s)')
    parser.add_argument('--unlearning_method', dest='unlearning_method', default='hybrid',
                        choices=['entropy_maximization', 'confidence_reduction', 'label_smoothing', 'adversarial', 'hybrid'],
                        help='遗忘方法: entropy_maximization(熵最大化), confidence_reduction(置信度降低), label_smoothing(标签平滑), adversarial(对抗性), hybrid(混合) (default: %(default)s)')

    # 注释掉不再使用的遗忘模式参数，现在使用新的遗忘方法
    # parser.add_argument('--unlearn_mode', dest='unlearn_mode', default='hybrid',
    #                     choices=['enhanced', 'adversarial', 'dual_teacher', 'surgical', 'hybrid', 'noise', 'selective'],
    #                     help='遗忘学习模式 - 已废弃，现在使用集成的对抗训练遗忘方法 (default: %(default)s)')

    ########################PAFL_ER#########################
    parser.add_argument('--ema', dest='ema', default=0.5, type=float,
                        help='指数移动平均系数，用于原型更新 (default: %(default)s)')
    parser.add_argument('--confidence_threshold', dest='confidence_threshold', default=0.5, type=float,
                        help='置信度阈值，用于基于置信度的遗忘策略 (default: %(default)s)')
    parser.add_argument('--distance_threshold', dest='distance_threshold', default=1.5, type=float,
                        help='原型距离阈值，用于基于原型距离的遗忘策略 (default: %(default)s)')
    parser.add_argument('--relevance_threshold', dest='relevance_threshold', default=0.3, type=float,
                        help='任务相关性阈值，用于基于任务相关性的遗忘策略 (default: %(default)s)')
    parser.add_argument('--forget_interval', dest='forget_interval', default=25, type=int,
                        help='遗忘操作间隔（轮数） (default: %(default)s)')
    parser.add_argument('--forget_weight', dest='forget_weight', default=0.5, type=float,
                        help='遗忘损失权重 (default: %(default)s)')

    ########################Optimizer#########################
    parser.add_argument('--optimizer', dest='optimizer', default='SGD', choices=['SGD', 'Adam'],
                        help='Optimizer (default: %(default)s)')
    parser.add_argument('--learning_rate', dest='learning_rate', default=0.05,
                        type=float,
                        help='Learning_rate (default: %(default)s)')
    parser.add_argument('--epoch', dest='epoch', default=20,
                        type=int,
                        help='The number of epochs used for one task. (default: %(default)s)')
    parser.add_argument('--batch', dest='batch', default=32,
                        type=int,
                        help='Batch size (default: %(default)s)')
    parser.add_argument('--test_batch', dest='test_batch', default=64,
                        type=int,
                        help='Test batch size (default: %(default)s)')
    parser.add_argument('--weight_decay', dest='weight_decay', type=float, default=0,
                        help='weight_decay')

    ########################遗忘机制高级配置#########################
    parser.add_argument('--forget_strategy', dest='forget_strategy', default='vote', choices=['vote', 'intersect', 'union'],
                        help='遗忘策略组合方式：vote-投票机制，intersect-取交集，union-取并集 (default: %(default)s)')
    parser.add_argument('--forget_vote_threshold', dest='forget_vote_threshold', default=2, type=int,
                        help='使用投票机制时，触发遗忘所需的最小票数 (default: %(default)s)')
    parser.add_argument('--max_forget_ratio', dest='max_forget_ratio', default=0.3, type=float,
                        help='单次遗忘的最大样本比例 (default: %(default)s)')
    parser.add_argument('--analyze_buffer', dest='analyze_buffer', default=False, type=boolean_string,
                        help='是否在每个epoch后分析缓冲区分布 (default: %(default)s)')
    parser.add_argument('--detailed_buffer_analysis', dest='detailed_buffer_analysis', default=False, type=boolean_string,
                        help='是否进行详细的缓冲区特征分析（会增加内存占用） (default: %(default)s)')
    parser.add_argument('--min_task_ratio', dest='min_task_ratio', default=0.05, type=float,
                        help='每个历史任务在缓冲区中的最小样本比例 (default: %(default)s)')
    parser.add_argument('--max_current_task_ratio', dest='max_current_task_ratio', default=0.5, type=float,
                        help='当前任务在缓冲区中的最大样本比例 (default: %(default)s)')
    
    # 选择性神经元遗忘参数
    parser.add_argument('--retention_ratio', dest='retention_ratio', default=0.5, type=float,
                        help='神经元保留比例，用于选择性神经元遗忘 (default: %(default)s)')
    
    # 双教师和对抗遗忘参数
    parser.add_argument('--kl_temperature', dest='kl_temperature', default=2.0, type=float,
                        help='知识蒸馏温度 (default: %(default)s)')
    parser.add_argument('--retain_weight', dest='retain_weight', default=5.0, type=float,
                        help='保留知识权重 (default: %(default)s)')
    
    # 对抗遗忘特定参数
    parser.add_argument('--epsilon', dest='epsilon', default=0.08, type=float,
                        help='对抗样本扰动大小 (default: %(default)s)')
    parser.add_argument('--adv_weight', dest='adv_weight', default=0.5, type=float,
                        help='对抗损失权重 (default: %(default)s)')
    parser.add_argument('--distill_weight', dest='distill_weight', default=0.3, type=float,
                        help='蒸馏损失权重 (default: %(default)s)')
    
    # 噪声注入遗忘参数
    parser.add_argument('--noise_scale', dest='noise_scale', default=0.1, type=float,
                        help='噪声比例，用于噪声注入遗忘 (default: %(default)s)')
    
    # 模型加载参数
    parser.add_argument('--load_model_path', dest='load_model_path', default='D:/continual_learning/output/models/paer_model_task2.pth', type=str,
                        help='已保存模型的路径，用于直接加载模型进行遗忘 (default: %(default)s)')
    
    ########################内存优化参数#########################
    parser.add_argument('--optimize_memory', dest='optimize_memory', default=False, type=boolean_string,
                        help='是否启用内存优化 (default: %(default)s)')
    parser.add_argument('--max_split_size_mb', dest='max_split_size_mb', default=128, type=int,
                        help='PyTorch CUDA内存分配器的最大分割大小(MB) (default: %(default)s)')
    parser.add_argument('--gpu_memory_fraction', dest='gpu_memory_fraction', default=0.8, type=float,
                        help='限制GPU内存使用比例 (default: %(default)s)')
    parser.add_argument('--deterministic', dest='deterministic', default=False, type=boolean_string,
                        help='启用确定性计算以减少内存使用 (default: %(default)s)')
    parser.add_argument('--half_precision', dest='half_precision', default=False, type=boolean_string,
                        help='使用半精度(FP16)计算以减少内存使用 (default: %(default)s)')
    parser.add_argument('--batch_size_test', dest='batch_size_test', default=8, type=int,
                        help='测试时的批处理大小，较小的值可减少内存使用 (default: %(default)s)')
    
    ########################Data#########################
    parser.add_argument('--num_tasks', dest='num_tasks', default=15,
                        type=int,
                        help='Number of tasks (default: %(default)s), OpenLORIS num_tasks is predefined')
    parser.add_argument('--fix_order', dest='fix_order', default=False,
                        type=boolean_string,
                        help='In NC scenario, should the class order be fixed (default: %(default)s)')
    parser.add_argument('--plot_sample', dest='plot_sample', default=False,
                        type=boolean_string,
                        help='In NI scenario, should sample images be plotted (default: %(default)s)')
    parser.add_argument('--data', dest='data', default="seed",
                        help='Path to the dataset. (default: %(default)s)')
    # parser.add_argument('--cl_type', dest='cl_type', default="ni", choices=['nc', 'ni'],
    #                     help='Continual learning type: new class "nc" or new instance "ni". (default: %(default)s)')
    # parser.add_argument('--ns_factor', dest='ns_factor', nargs='+',
    #                     default=(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0), type=float,
    #                     help='Change factor for non-stationary data(default: %(default)s)')
    # parser.add_argument('--ns_type', dest='ns_type', default='noise', type=str, choices=['noise', 'occlusion', 'blur'],
    #                     help='Type of non-stationary (default: %(default)s)')
    # parser.add_argument('--ns_task', dest='ns_task', nargs='+', default=(1, 1, 2, 2, 2, 2), type=int,
    #                     help='NI Non Stationary task composition (default: %(default)s)')
    # parser.add_argument('--online', dest='online', default=True,
    #                     type=boolean_string,
    #                     help='If False, offline training will be performed (default: %(default)s)')

    ########################ER#########################
    parser.add_argument('--mem_size', dest='mem_size', default=2000,
                        type=int,
                        help='Memory buffer size (default: %(default)s)')
    parser.add_argument('--eps_mem_batch', dest='eps_mem_batch', default=16,
                        type=int,
                        help='Episode memory per batch (default: %(default)s)')

    ########################EWC##########################
    parser.add_argument('--lambda', dest='lambda_', default=100, type=float,
                        help='EWC regularization coefficient')
    parser.add_argument('--alpha', dest='alpha', default=0.9, type=float,
                        help='EWC++ exponential moving average decay for Fisher calculation at each step')
    parser.add_argument('--fisher_update_after', dest='fisher_update_after', type=int, default=50,
                        help="Number of training iterations after which the Fisher will be updated.")
    parser.add_argument('--grad_clip_norm', dest='grad_clip_norm', type=float, default=1.0,
                        help="Maximum norm for gradient clipping to improve stability.")

    ########################MIR#########################
    parser.add_argument('--subsample', dest='subsample', default=50,
                        type=int,
                        help='Number of subsample to perform MIR(default: %(default)s)')

    ########################GSS#########################
    parser.add_argument('--gss_mem_strength', dest='gss_mem_strength', default=10, type=int,
                        help='Number of batches randomly sampled from memory to estimate score')
    parser.add_argument('--gss_batch_size', dest='gss_batch_size', default=10, type=int,
                        help='Random sampling batch size to estimate score')

    ########################ASER########################
    parser.add_argument('--k', dest='k', default=5,
                        type=int,
                        help='Number of nearest neighbors (K) to perform ASER (default: %(default)s)')

    parser.add_argument('--aser_type', dest='aser_type', default="asvm", type=str, choices=['neg_sv', 'asv', 'asvm'],
                        help='Type of ASER: '
                             '"neg_sv" - Use negative SV only,'
                             ' "asv" - Use extremal values of Adversarial SV and Cooperative SV,'
                             ' "asvm" - Use mean values of Adversarial SV and Cooperative SV')

    parser.add_argument('--n_smp_cls', dest='n_smp_cls', default=2.0,
                        type=float,
                        help='Maximum number of samples per class for random sampling (default: %(default)s)')

    ########################CNDPM#########################
    parser.add_argument('--stm_capacity', dest='stm_capacity', default=1000, type=int, help='Short term memory size')
    parser.add_argument('--classifier_chill', dest='classifier_chill', default=0.01, type=float,
                        help='NDPM classifier_chill')
    parser.add_argument('--log_alpha', dest='log_alpha', default=-300, type=float, help='Prior log alpha')

    ########################GDumb#########################
    parser.add_argument('--minlr', dest='minlr', default=0.0005, type=float, help='Minimal learning rate')
    parser.add_argument('--clip', dest='clip', default=10., type=float,
                        help='value for gradient clipping')
    parser.add_argument('--mem_epoch', dest='mem_epoch', default=70, type=int, help='Epochs to train for memory')

    #######################Tricks#########################
    parser.add_argument('--labels_trick', dest='labels_trick', default=False, type=boolean_string,
                        help='Labels trick')
    parser.add_argument('--separated_softmax', dest='separated_softmax', default=False, type=boolean_string,
                        help='separated softmax')
    parser.add_argument('--kd_trick', dest='kd_trick', default=False, type=boolean_string,
                        help='Knowledge distillation with cross entropy trick')
    parser.add_argument('--kd_trick_star', dest='kd_trick_star', default=False, type=boolean_string,
                        help='Improved knowledge distillation trick')
    parser.add_argument('--review_trick', dest='review_trick', default=False, type=boolean_string,
                        help='Review trick')
    parser.add_argument('--ncm_trick', dest='ncm_trick', default=False, type=boolean_string,
                        help='Use nearest class mean classifier')
    parser.add_argument('--mem_iters', dest='mem_iters', default=1, type=int,
                        help='mem_iters')

    ####################Early Stopping######################
    parser.add_argument('--min_delta', dest='min_delta', default=0., type=float,
                        help='A minimum increase in the score to qualify as an improvement')
    parser.add_argument('--patience', dest='patience', default=0, type=int,
                        help='Number of events to wait if no improvement and then stop the training.')
    parser.add_argument('--cumulative_delta', dest='cumulative_delta', default=False, type=boolean_string,
                        help='If True, `min_delta` defines an increase since the last `patience` reset, '
                             'otherwise, it defines an increase after the last event.')

    ####################SupContrast######################
    parser.add_argument('--temp', type=float, default=0.07,
                        help='temperature for loss function')
    parser.add_argument('--buffer_tracker', type=boolean_string, default=False,
                        help='Keep track of buffer with a dictionary')
    parser.add_argument('--warmup', type=int, default=4,
                        help='warmup of buffer before retrieve')
    parser.add_argument('--head', type=str, default='mlp',
                        help='projection head')
    args = parser.parse_args()
    args.cuda = torch.cuda.is_available()
    main(args)