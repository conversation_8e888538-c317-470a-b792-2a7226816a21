{".class": "MypyFile", "_fullname": "utils.forgetting_scheduler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "ForgettingScheduler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "utils.forgetting_scheduler.ForgettingScheduler", "name": "ForgettingScheduler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "utils.forgetting_scheduler", "mro": ["utils.forgetting_scheduler.ForgettingScheduler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "forget_task_ids", "total_tasks", "unlearning_epochs", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "forget_task_ids", "total_tasks", "unlearning_epochs", "verbose"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ForgettingScheduler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evaluate_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "agent", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler._evaluate_accuracy", "name": "_evaluate_accuracy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "agent", "x", "y"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_evaluate_accuracy of ForgettingScheduler", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evaluate_test_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent", "test_loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler._evaluate_test_accuracy", "name": "_evaluate_test_accuracy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent", "test_loader"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_evaluate_test_accuracy of ForgettingScheduler", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_perform_model_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "agent", "forget_task_id", "test_loaders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler._perform_model_unlearning", "name": "_perform_model_unlearning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "agent", "forget_task_id", "test_loaders"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_perform_model_unlearning of ForgettingScheduler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_print_forgetting_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler._print_forgetting_results", "name": "_print_forgetting_results", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "results"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_print_forgetting_results of ForgettingScheduler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unlearning_training": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "agent", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler._unlearning_training", "name": "_unlearning_training", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "agent", "forget_x", "forget_y", "retain_x", "retain_y"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unlearning_training of ForgettingScheduler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.current_task", "name": "current_task", "type": "builtins.int"}}, "evaluate_membership_inference_attack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "agent", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.evaluate_membership_inference_attack", "name": "evaluate_membership_inference_attack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "agent", "forget_x", "forget_y", "retain_x", "retain_y"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evaluate_membership_inference_attack of ForgettingScheduler", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_forgetting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "agent", "task_id", "test_loaders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.execute_forgetting", "name": "execute_forgetting", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "agent", "task_id", "test_loaders"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_forgetting of ForgettingScheduler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forget_task_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.forget_task_ids", "name": "forget_task_ids", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "forgetting_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.forgetting_results", "name": "forgetting_results", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "forgotten_tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.forgotten_tasks", "name": "forgotten_tasks", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "get_forgetting_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.get_forgetting_summary", "name": "get_forgetting_summary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_forgetting_summary of ForgettingScheduler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tasks_to_forget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "completed_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.get_tasks_to_forget", "name": "get_tasks_to_forget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "completed_task_id"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tasks_to_forget of ForgettingScheduler", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_forget_after_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.should_forget_after_task", "name": "should_forget_after_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_forget_after_task of ForgettingScheduler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "total_tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.total_tasks", "name": "total_tasks", "type": "builtins.int"}}, "unlearning_epochs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.unlearning_epochs", "name": "unlearning_epochs", "type": "builtins.int"}}, "validate_forgetting_quality": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "agent", "forget_task_id", "test_loaders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.validate_forgetting_quality", "name": "validate_forgetting_quality", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "agent", "forget_task_id", "test_loaders"], "arg_types": ["utils.forgetting_scheduler.ForgettingScheduler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_forgetting_quality of ForgettingScheduler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verbose": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.forgetting_scheduler.ForgettingScheduler.verbose", "name": "verbose", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "utils.forgetting_scheduler.ForgettingScheduler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "utils.forgetting_scheduler.ForgettingScheduler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.forgetting_scheduler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.forgetting_scheduler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.forgetting_scheduler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.forgetting_scheduler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.forgetting_scheduler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.forgetting_scheduler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "roc_auc_score": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "utils.forgetting_scheduler.roc_auc_score", "name": "roc_auc_score", "type": {".class": "AnyType", "missing_import_name": "utils.forgetting_scheduler.roc_auc_score", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\utils\\forgetting_scheduler.py"}