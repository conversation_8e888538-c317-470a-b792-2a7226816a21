{"data_mtime": 1754308998, "dep_lines": [15, 14, 6, 7, 8, 9, 16, 220, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["utils.buffer.buffer", "utils.forgetting_scheduler", "torch", "numpy", "sys", "os", "<PERSON><PERSON><PERSON><PERSON>", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.container", "torch.nn.modules.flatten", "torch.nn.modules.linear", "torch.nn.modules.module", "typing", "utils", "utils.buffer"], "hash": "78b139a629f69d879779a419af084aaa60e5593d", "id": "test_buffer_removal", "ignore_all": false, "interface_hash": "774b08cf6d2f12e71914d8a6caa7ec1febe35f4f", "mtime": 1754308988, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\test_buffer_removal.py", "plugin_data": null, "size": 8344, "suppressed": [], "version_id": "1.15.0"}