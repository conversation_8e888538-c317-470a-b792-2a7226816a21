{"data_mtime": 1754303748, "dep_lines": [14, 15, 16, 6, 7, 8, 9, 17, 86, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["utils.buffer.buffer", "utils.buffer.reservoir_update", "utils.setup_elements", "torch", "numpy", "sys", "os", "<PERSON><PERSON><PERSON><PERSON>", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy.random", "torch._C", "torch._tensor", "torch.cuda", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "typing", "utils", "utils.buffer"], "hash": "cdbfb72ff5cd7a8266c1be9a050950d9a3be4cb6", "id": "test_device_fix", "ignore_all": false, "interface_hash": "27a23486ca1545e74b96ef1bfd1f6d89cae36f1c", "mtime": 1754303738, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\test_device_fix.py", "plugin_data": null, "size": 7718, "suppressed": [], "version_id": "1.15.0"}