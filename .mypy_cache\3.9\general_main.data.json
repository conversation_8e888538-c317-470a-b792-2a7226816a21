{".class": "MypyFile", "_fullname": "general_main", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClassificationHead": {".class": "SymbolTableNode", "cross_ref": "models.temp_model.ClassificationHead", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "general_main.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "general_main.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "general_main.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "general_main.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "general_main.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "general_main.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "general_main.args", "name": "args", "type": "argparse.Namespace"}}, "boolean_string": {".class": "SymbolTableNode", "cross_ref": "utils.utils.boolean_string", "kind": "Gdef"}, "continual_learning_EEG_run": {".class": "SymbolTableNode", "cross_ref": "temp_de.continual_learning_EEG_run", "kind": "Gdef"}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef"}, "load_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "general_main.load_model", "name": "load_model", "type": null}}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "general_main.main", "name": "main", "type": null}}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "name_match": {".class": "SymbolTableNode", "cross_ref": "utils.name_match", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "general_main.parser", "name": "parser", "type": "argparse.ArgumentParser"}}, "run_training": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "general_main.run_training", "name": "run_training", "type": null}}, "setup_architecture": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.setup_architecture", "kind": "Gdef"}, "setup_memory_optimization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "general_main.setup_memory_optimization", "name": "setup_memory_optimization", "type": null}}, "setup_opt": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.setup_opt", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\general_main.py"}