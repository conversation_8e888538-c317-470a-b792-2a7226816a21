"""
测试新的遗忘损失函数
验证不同遗忘方法的稳定性和效果
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.forgetting_scheduler import ForgettingScheduler


def test_unlearning_loss_stability():
    """测试遗忘损失函数的稳定性"""
    print("======= 测试遗忘损失函数稳定性 =======")
    
    # 创建测试数据
    batch_size = 32
    num_classes = 3
    
    # 模拟不同置信度的logits
    test_cases = [
        ("低置信度", torch.randn(batch_size, num_classes) * 0.5),
        ("中等置信度", torch.randn(batch_size, num_classes) * 1.0),
        ("高置信度", torch.randn(batch_size, num_classes) * 2.0),
        ("极高置信度", torch.randn(batch_size, num_classes) * 5.0),
    ]
    
    labels = torch.randint(0, num_classes, (batch_size,))
    
    # 创建遗忘调度器
    scheduler = ForgettingScheduler([1], 3, verbose=False)
    
    methods = ['entropy_maximization', 'confidence_reduction', 'label_smoothing', 'adversarial', 'hybrid']
    
    results = {}
    
    for method in methods:
        print(f"\n测试方法: {method}")
        method_results = []
        
        for case_name, logits in test_cases:
            try:
                loss = scheduler._compute_unlearning_loss(logits, labels, method)
                loss_value = loss.item()
                
                # 检查是否为NaN或无穷大
                is_stable = not (np.isnan(loss_value) or np.isinf(loss_value))
                
                print(f"  {case_name}: Loss={loss_value:.4f}, 稳定={is_stable}")
                method_results.append((case_name, loss_value, is_stable))
                
            except Exception as e:
                print(f"  {case_name}: 错误 - {e}")
                method_results.append((case_name, float('nan'), False))
        
        results[method] = method_results
    
    # 分析结果
    print("\n======= 稳定性分析 =======")
    for method, method_results in results.items():
        stable_count = sum(1 for _, _, stable in method_results if stable)
        print(f"{method}: {stable_count}/{len(method_results)} 个测试用例稳定")
    
    return results


def test_unlearning_effectiveness():
    """测试遗忘效果"""
    print("\n======= 测试遗忘效果 =======")
    
    # 创建一个简单的模型
    class SimpleModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.fc = torch.nn.Linear(10, 3)
        
        def forward(self, x):
            return self.fc(x)
    
    model = SimpleModel()
    optimizer = torch.optim.SGD(model.parameters(), lr=0.01)
    
    # 创建训练数据
    batch_size = 100
    input_dim = 10
    num_classes = 3
    
    # 遗忘数据（我们希望模型忘记这些）
    forget_x = torch.randn(batch_size, input_dim)
    forget_y = torch.randint(0, num_classes, (batch_size,))
    
    # 保留数据（我们希望模型记住这些）
    retain_x = torch.randn(batch_size, input_dim)
    retain_y = torch.randint(0, num_classes, (batch_size,))
    
    # 创建遗忘调度器
    scheduler = ForgettingScheduler([1], 3, verbose=False)
    
    methods = ['entropy_maximization', 'confidence_reduction', 'hybrid']
    
    for method in methods:
        print(f"\n测试方法: {method}")
        
        # 重置模型
        model = SimpleModel()
        optimizer = torch.optim.SGD(model.parameters(), lr=0.01)
        
        # 先训练模型学习所有数据
        print("  阶段1: 学习所有数据")
        for epoch in range(50):
            # 训练遗忘数据
            optimizer.zero_grad()
            forget_logits = model(forget_x)
            forget_loss = F.cross_entropy(forget_logits, forget_y)
            forget_loss.backward()
            optimizer.step()
            
            # 训练保留数据
            optimizer.zero_grad()
            retain_logits = model(retain_x)
            retain_loss = F.cross_entropy(retain_logits, retain_y)
            retain_loss.backward()
            optimizer.step()
        
        # 评估学习后的准确率
        with torch.no_grad():
            forget_logits = model(forget_x)
            forget_acc_before = (torch.argmax(forget_logits, dim=1) == forget_y).float().mean().item()
            
            retain_logits = model(retain_x)
            retain_acc_before = (torch.argmax(retain_logits, dim=1) == retain_y).float().mean().item()
        
        print(f"    学习后 - 遗忘数据准确率: {forget_acc_before:.4f}, 保留数据准确率: {retain_acc_before:.4f}")
        
        # 执行遗忘训练
        print("  阶段2: 执行遗忘")
        for epoch in range(30):
            optimizer.zero_grad()
            
            # 遗忘损失
            forget_logits = model(forget_x)
            forget_loss = scheduler._compute_unlearning_loss(forget_logits, forget_y, method)
            
            # 保留损失
            retain_logits = model(retain_x)
            retain_loss = F.cross_entropy(retain_logits, retain_y)
            
            # 总损失
            total_loss = forget_loss + 2.0 * retain_loss
            total_loss.backward()
            optimizer.step()
        
        # 评估遗忘后的准确率
        with torch.no_grad():
            forget_logits = model(forget_x)
            forget_acc_after = (torch.argmax(forget_logits, dim=1) == forget_y).float().mean().item()
            
            retain_logits = model(retain_x)
            retain_acc_after = (torch.argmax(retain_logits, dim=1) == retain_y).float().mean().item()
        
        print(f"    遗忘后 - 遗忘数据准确率: {forget_acc_after:.4f}, 保留数据准确率: {retain_acc_after:.4f}")
        
        # 分析遗忘效果
        forget_drop = forget_acc_before - forget_acc_after
        retain_drop = retain_acc_before - retain_acc_after
        
        print(f"    效果分析 - 遗忘数据下降: {forget_drop:.4f}, 保留数据下降: {retain_drop:.4f}")
        
        # 评估遗忘质量
        random_acc = 1.0 / num_classes
        forget_quality = abs(forget_acc_after - random_acc) < 0.15  # 接近随机
        retain_quality = abs(retain_drop) < 0.1  # 保留数据影响小
        
        print(f"    质量评估 - 遗忘质量: {'✓' if forget_quality else '✗'}, 保留质量: {'✓' if retain_quality else '✗'}")


def test_loss_convergence():
    """测试损失收敛性"""
    print("\n======= 测试损失收敛性 =======")
    
    batch_size = 50
    num_classes = 3
    
    # 创建固定的测试数据
    torch.manual_seed(42)
    logits = torch.randn(batch_size, num_classes, requires_grad=True)
    labels = torch.randint(0, num_classes, (batch_size,))
    
    scheduler = ForgettingScheduler([1], 3, verbose=False)
    
    methods = ['entropy_maximization', 'confidence_reduction', 'hybrid']
    
    for method in methods:
        print(f"\n测试方法: {method}")
        
        # 重置logits
        test_logits = torch.randn(batch_size, num_classes, requires_grad=True)
        optimizer = torch.optim.SGD([test_logits], lr=0.01)
        
        losses = []
        
        # 训练多个步骤
        for step in range(100):
            optimizer.zero_grad()
            loss = scheduler._compute_unlearning_loss(test_logits, labels, method)
            loss.backward()
            optimizer.step()
            
            losses.append(loss.item())
            
            # 检查是否出现NaN
            if np.isnan(loss.item()):
                print(f"  步骤 {step}: 出现NaN，训练不稳定")
                break
        
        if len(losses) == 100:
            print(f"  训练稳定，最终损失: {losses[-1]:.4f}")
            print(f"  损失范围: [{min(losses):.4f}, {max(losses):.4f}]")
        
        # 检查收敛性
        if len(losses) >= 50:
            recent_losses = losses[-10:]
            loss_std = np.std(recent_losses)
            converged = loss_std < 0.1
            print(f"  收敛性: {'✓' if converged else '✗'} (最近10步标准差: {loss_std:.4f})")


def main():
    """主测试函数"""
    print("开始测试新的遗忘损失函数...")
    print("=" * 50)
    
    try:
        # 测试稳定性
        stability_results = test_unlearning_loss_stability()
        
        # 测试效果
        test_unlearning_effectiveness()
        
        # 测试收敛性
        test_loss_convergence()
        
        print("\n" + "=" * 50)
        print("测试完成！")
        
        # 推荐最佳方法
        print("\n推荐使用方法:")
        print("1. hybrid: 平衡了稳定性和效果")
        print("2. entropy_maximization: 理论基础强，适合大多数情况")
        print("3. confidence_reduction: 简单有效，计算开销小")
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
