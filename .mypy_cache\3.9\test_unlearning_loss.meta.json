{"data_mtime": 1754306238, "dep_lines": [7, 7, 9, 16, 6, 8, 9, 10, 11, 263, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 10, 10, 20, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "torch.nn", "matplotlib.pyplot", "utils.forgetting_scheduler", "torch", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sys", "os", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "torch._C", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.optim", "torch.optim.optimizer", "torch.optim.sgd", "torch.utils", "torch.utils._contextlib", "typing", "typing_extensions", "utils"], "hash": "5f3cc300725d2374b4abd7cea924aeabc9af2411", "id": "test_unlearning_loss", "ignore_all": false, "interface_hash": "798de0e410445e5e34e33133dfd21e9ce6710498", "mtime": 1754306230, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\test_unlearning_loss.py", "plugin_data": null, "size": 9313, "suppressed": [], "version_id": "1.15.0"}