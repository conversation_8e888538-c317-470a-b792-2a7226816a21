{"data_mtime": 1754307754, "dep_lines": [5, 5, 6, 7, 9, 10, 13, 1, 2, 3, 4, 11, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [10, 20, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.utils.data", "torch.utils", "utils.setup_elements", "utils.name_match", "utils.utils", "models.temp_model", "agents.base", "pickle", "random", "time", "torch", "numpy", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.utils.data.dataset", "typing"], "hash": "d9519df2edd65661d0e57bd87baf6e45ad3c7d8e", "id": "temp_de", "ignore_all": true, "interface_hash": "c7fefadb3b467cdc7f9e310466942f29a2454430", "mtime": 1754307643, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\temp_de.py", "plugin_data": null, "size": 18567, "suppressed": ["sklearn.preprocessing"], "version_id": "1.15.0"}