import pickle
import random
import time
import torch
from torch.utils import data
from utils.setup_elements import transforms_match
from utils.name_match import agents
from utils.setup_elements import setup_opt, setup_architecture
from utils.utils import maybe_cuda
from models.temp_model import ClassificationHead
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from agents.base import SequentialWrapper

def continual_learning_EEG_run(params, store=False, save_path=None):
    # data_file_in = ['1_20131027', '2_20140404', '3_20140603', '4_20140621', '5_20140411', '6_20130712',
    #                 '7_20131027', '8_20140511', '9_20140620', '10_20131130', '11_20140618', '12_20131127',
    #                 '13_20140527', '14_20140601', '15_20130709']##session1
    data_file_in = ['1_20131027', '2_20140404', '3_20140603']##session1

    # data_file_in = ['1_20131030', '2_20140413', '3_20140611', '4_20140702', '5_20140418', '6_20131016',
    #                 '7_20131030', '8_20140514', '9_20140627', '10_20131204', '11_20140625', '12_20131201',
    #                 '13_20140603', '14_20140615', '15_20131016']##session2

    # data_file_in = ['1_20131107', '2_20140419', '3_20140629', '4_20140705', '5_20140506', '6_20131113',
    #                 '7_20131106', '8_20140521', '9_20140704', '10_20131211', '11_20140630', '12_20131207',
    #                 '13_20140610', '14_20140624', '15_20131105']##session3

    # data_file_in = [['1_20131027', '1_20131030', '1_20131107'],
    #                 ['2_20140404', '2_20140413', '2_20140419'],
    #                 ['3_20140603', '3_20140611', '3_20140629'],
    #                 ['4_20140621', '4_20140702', '4_20140705'],
    #                 ['5_20140411', '5_20140418', '5_20140506'],
    #                 ['6_20130712', '6_20131016', '6_20131113'],
    #                 ['7_20131027', '7_20131030', '7_20131106'],
    #                 ['8_20140511', '8_20140514', '8_20140521'],
    #                 ['9_20140620', '9_20140627', '9_20140704'],
    #                 ['10_20131130', '10_20131204', '10_20131211'],
    #                 ['11_20140618', '11_20140625', '11_20140630'],
    #                 ['12_20131127', '12_20131201', '12_20131207'],
    #                 ['13_20140527', '13_20140603', '13_20140610'],
    #                 ['14_20140601', '14_20140615', '14_20140624'],
    #                 ['15_20130709', '15_20131016', '15_20131105']]##session1+2+3

    # random.shuffle(data_file_in)
    print(data_file_in)
    start = time.time()
    data_end = time.time()
    print('data setup time: {}'.format(data_end - start))

    temp_acc = []

    print("======= 模型和优化器设置 =======")
    model_ecoder = setup_architecture(params)
    model_ecoder = maybe_cuda(model_ecoder, params.cuda)
    model_Cls = ClassificationHead(64, 3)
    model_Cls = maybe_cuda(model_Cls, params.cuda)

    opt = setup_opt(params.optimizer, model_ecoder, model_Cls, params.learning_rate, params.weight_decay)

    print(f"======= 初始化代理: {params.agent} =======")
    print(f"更新方法: {params.update}, 检索方法: {params.retrieve}")
    # 使用统一的接口初始化代理
    # 将编码器和分类器作为元组传递给所有代理类
    agent = agents[params.agent]((model_ecoder, model_Cls), opt, params)

    # 初始化遗忘调度器
    forgetting_scheduler = None
    if hasattr(params, 'forget_task_ids') and params.forget_task_ids:
        from utils.forgetting_scheduler import ForgettingScheduler
        forget_task_ids = params.forget_task_ids if isinstance(params.forget_task_ids, list) else [params.forget_task_ids]
        forgetting_scheduler = ForgettingScheduler(
            forget_task_ids=forget_task_ids,
            total_tasks=len(data_file_in),
            unlearning_epochs=getattr(params, 'unlearning_epochs', 20),
            verbose=getattr(params, 'verbose', True),
            unlearning_method=getattr(params, 'unlearning_method', 'hybrid')
        )
        print(f"======= 遗忘调度器已初始化 =======")
        print(f"要遗忘的任务: {forget_task_ids}")
    elif hasattr(params, 'forget_task_id') and params.forget_task_id is not None and params.forget_task_id >= 0:
        # 兼容旧的单任务遗忘参数
        from utils.forgetting_scheduler import ForgettingScheduler
        forgetting_scheduler = ForgettingScheduler(
            forget_task_ids=[params.forget_task_id],
            total_tasks=len(data_file_in),
            unlearning_epochs=getattr(params, 'unlearning_epochs', 20),
            verbose=getattr(params, 'verbose', True),
            unlearning_method=getattr(params, 'unlearning_method', 'hybrid')
        )
        print(f"======= 遗忘调度器已初始化 =======")
        print(f"要遗忘的任务: {params.forget_task_id}")

    test_loaders = []
    for i in range(len(data_file_in)):
        print(f"\n======= 处理主题 {data_file_in[i]} ({i+1}/{len(data_file_in)}) =======")
        try:
            subject_train_data_temp, subject_train_data_temp_label, subject_test_data_temp, subject_test_data_temp_label = get_loader(data_file_in[i])

            # 打印标签分布
            unique_labels, counts = np.unique(subject_train_data_temp_label, return_counts=True)
            print(f"训练数据标签分布: {dict(zip(unique_labels, counts))}")
            unique_labels, counts = np.unique(subject_test_data_temp_label, return_counts=True)
            print(f"测试数据标签分布: {dict(zip(unique_labels, counts))}")

            test_loaders.append(setup_test_loader(subject_test_data_temp, subject_test_data_temp_label, params))
            print("-----------training batch {}-------------".format(i))
            print('size: {}, {}'.format(subject_train_data_temp.shape, subject_train_data_temp_label.shape))

            # 检查数据类型
            print(f"训练数据类型: {type(subject_train_data_temp)}, 标签类型: {type(subject_train_data_temp_label)}")
            print(f"训练数据范围: [{subject_train_data_temp.min()}, {subject_train_data_temp.max()}]")

            # 训练学习器，传递任务ID
            train_start = time.time()
            # 将当前循环索引作为任务ID传递
            agent.train_learner(subject_train_data_temp, subject_train_data_temp_label, task_id=i)
            train_end = time.time()
            print('训练时间: {:.2f}秒'.format(train_end - train_start))

            # ======= 遗忘检查点：在任务训练完成后检查是否需要遗忘 =======
            if forgetting_scheduler is not None:
                if forgetting_scheduler.should_forget_after_task(i):
                    tasks_to_forget = forgetting_scheduler.get_tasks_to_forget(i)
                    for forget_task_id in tasks_to_forget:
                        print(f"\n======= 执行遗忘操作：任务 {forget_task_id} =======")
                        try:
                            # 执行遗忘
                            forget_results = forgetting_scheduler.execute_forgetting(
                                agent=agent,
                                task_id=forget_task_id,
                                test_loaders=test_loaders
                            )

                            # 可以在这里保存遗忘结果
                            if store and save_path:
                                import os
                                import datetime
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                forget_result_path = os.path.join(save_path, f"forget_results_task{forget_task_id}_{timestamp}.json")
                                import json
                                with open(forget_result_path, 'w') as f:
                                    # 转换numpy类型为Python原生类型以便JSON序列化
                                    serializable_results = {}
                                    for k, v in forget_results.items():
                                        if isinstance(v, np.ndarray):
                                            serializable_results[k] = v.tolist()
                                        elif isinstance(v, (np.integer, np.floating)):
                                            serializable_results[k] = v.item()
                                        else:
                                            serializable_results[k] = v
                                    json.dump(serializable_results, f, indent=2)
                                print(f"遗忘结果已保存到: {forget_result_path}")

                        except Exception as forget_error:
                            print(f"执行遗忘操作时出错: {forget_error}")
                            import traceback
                            traceback.print_exc()

            # 评估
            eval_start = time.time()
            acc_array = agent.evaluate(test_loaders)
            eval_end = time.time()
            print('评估时间: {:.2f}秒'.format(eval_end - eval_start))

            temp_acc.append(acc_array)
            print("-----------avg_end_acc {}-----------".format(np.mean(temp_acc[-1])))
        except Exception as e:
            print(f"处理主题 {data_file_in[i]} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 打印最终准确率摘要
    print("\n======= 最终准确率摘要 =======")
    for i, acc in enumerate(temp_acc):
        print(f"主题 {data_file_in[i]}: {acc}, 平均: {np.mean(acc):.4f}")
    
    final_avg = np.mean([np.mean(acc) for acc in temp_acc])
    print(f"总平均准确率: {final_avg:.4f}")
    
    # 返回代理和准确率数组，供主程序使用
    return agent, temp_acc


def shuffle_data(x, y):
    perm_inds = np.arange(0, x.shape[0])
    np.random.shuffle(perm_inds)
    rdm_x = x[perm_inds]
    rdm_y = y[perm_inds]
    return rdm_x, rdm_y

class dataset_transform(data.Dataset):
    def __init__(self, x, y, transform=None):
        self.x = x
        self.y = torch.from_numpy(y)
        self.transform = transform  # save the transform

    def __len__(self):
        return len(self.y)  # self.x.shape[0]  # return 1 as we have only one image

    def __getitem__(self, idx):
        # return the augmented image
        if self.transform:
            x = torch.from_numpy(self.x[idx])
        else:
            # x = self.x[idx]
            x = torch.from_numpy(self.x[idx])

        return x.float(), self.y[idx]

def setup_test_loader(x_test, y_test, params):

    test_dataset = dataset_transform(x_test, y_test, transform=transforms_match[params.data])
    test_loader = data.DataLoader(test_dataset, batch_size=params.test_batch, shuffle=True, num_workers=0)

    return test_loader

def get_loader(data_file):  ##数据加载：加载SEED数据集的微分熵特征
    cnn_suffix = ".mat_de_train_dataset.pkl"
    label_suffix = ".mat_de_train_labels.pkl"
    dataset_dir = "D:/continual_learning/EEG_data/SEED_DE_session1_9_6/"

    # 加载data和labels：
    with open(dataset_dir + data_file + cnn_suffix, 'rb') as fp:
        source_subject_data = pickle.load(fp)
        source_subject_data = np.transpose(source_subject_data.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
    with open(dataset_dir + data_file + label_suffix, 'rb') as fp:
        source_subject_labels = pickle.load(fp)

    source_subject_data, source_subject_labels = shuffle_data(source_subject_data, source_subject_labels)

    cnn_suffix = ".mat_de_test_dataset.pkl"
    label_suffix = ".mat_de_test_labels.pkl"
    dataset_dir = "D:/continual_learning/EEG_data/SEED_DE_session1_9_6/"

    # 加载data和labels：
    with open(dataset_dir + data_file + cnn_suffix, 'rb') as fp:
        target_subject_data = pickle.load(fp)
        target_subject_data = np.transpose(target_subject_data.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
    with open(dataset_dir + data_file + label_suffix, 'rb') as fp:
        target_subject_labels = pickle.load(fp)
    target_subject_data, target_subject_labels = shuffle_data(target_subject_data, target_subject_labels)

    return source_subject_data, source_subject_labels, target_subject_data, target_subject_labels


# def get_loader(data_file):  ##62*5
#     cnn_suffix = ".mat_de_62_5_train_dataset.pkl"
#     label_suffix = ".mat_de_62_5_train_labels.pkl"
#     dataset_dir = "./EEG_data/SEED_DE_62_5/session1/"
#
#     # 加载data和labels：
#     with open(dataset_dir + data_file + cnn_suffix, 'rb') as fp:
#         source_subject_data = pickle.load(fp)
#         source_subject_data = np.transpose(source_subject_data.reshape(-1, 62, 5, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file + label_suffix, 'rb') as fp:
#         source_subject_labels = pickle.load(fp)
#
#     source_subject_data, source_subject_labels = shuffle_data(source_subject_data, source_subject_labels)
#
#     cnn_suffix = ".mat_de_62_5_test_dataset.pkl"
#     label_suffix = ".mat_de_62_5_test_labels.pkl"
#     dataset_dir = "./EEG_data/SEED_DE_62_5/session1/"
#
#     # 加载data和labels：
#     with open(dataset_dir + data_file + cnn_suffix, 'rb') as fp:
#         target_subject_data = pickle.load(fp)
#         target_subject_data = np.transpose(target_subject_data.reshape(-1, 62, 5, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file + label_suffix, 'rb') as fp:
#         target_subject_labels = pickle.load(fp)
#     target_subject_data, target_subject_labels = shuffle_data(target_subject_data, target_subject_labels)
#
#     return source_subject_data, source_subject_labels, target_subject_data, target_subject_labels\

# def get_loader(data_file):  ##train:session1+session2;test:session3
#
#     cnn_suffix = ".mat_de_train_dataset.pkl"
#     label_suffix = ".mat_de_train_labels.pkl"
#
#
#     # 加载data和labels：
#     dataset_dir = "E:/1_myresearch/Code/Trial/EEG_data/SEED_DE_session1_9_6/"
#     with open(dataset_dir + data_file[0] + cnn_suffix, 'rb') as fp:
#         source_subject_data1 = pickle.load(fp)
#         source_subject_data1 = np.transpose(source_subject_data1.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file [0]+ label_suffix, 'rb') as fp:
#         source_subject_labels1 = pickle.load(fp)
#
#     source_subject_data1, source_subject_labels1 = shuffle_data(source_subject_data1, source_subject_labels1)
#     # 加载data和labels：
#     dataset_dir = "E:/1_myresearch/Code/Trial/EEG_data/SEED_DE_session2_9_6/"
#     with open(dataset_dir + data_file[1] + cnn_suffix, 'rb') as fp:
#         source_subject_data2 = pickle.load(fp)
#         source_subject_data2 = np.transpose(source_subject_data2.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file [1]+ label_suffix, 'rb') as fp:
#         source_subject_labels2 = pickle.load(fp)
#
#     source_subject_data2, source_subject_labels2 = shuffle_data(source_subject_data2, source_subject_labels2)
#     # 加载data和labels：
#     dataset_dir = "E:/1_myresearch/Code/Trial/EEG_data/SEED_DE_session3_9_6/"
#     with open(dataset_dir + data_file [2] + cnn_suffix, 'rb') as fp:
#         source_subject_data3 = pickle.load(fp)
#         source_subject_data3 = np.transpose(source_subject_data3.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file [2]+ label_suffix, 'rb') as fp:
#         source_subject_labels3 = pickle.load(fp)
#
#     source_subject_data3, source_subject_labels3 = shuffle_data(source_subject_data3, source_subject_labels3)
#
#     cnn_suffix = ".mat_de_test_dataset.pkl"
#     label_suffix = ".mat_de_test_labels.pkl"
#
#     # 加载data和labels：
#     dataset_dir = "E:/1_myresearch/Code/Trial/EEG_data/SEED_DE_session1_9_6/"
#     with open(dataset_dir + data_file [0] + cnn_suffix, 'rb') as fp:
#         target_subject_data1 = pickle.load(fp)
#         target_subject_data1 = np.transpose(target_subject_data1.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file [0] + label_suffix, 'rb') as fp:
#         target_subject_labels1 = pickle.load(fp)
#     target_subject_data1, target_subject_labels1 = shuffle_data(target_subject_data1, target_subject_labels1)
#     # 加载data和labels：
#     dataset_dir = "E:/1_myresearch/Code/Trial/EEG_data/SEED_DE_session2_9_6/"
#     with open(dataset_dir + data_file [1] + cnn_suffix, 'rb') as fp:
#         target_subject_data2 = pickle.load(fp)
#         target_subject_data2 = np.transpose(target_subject_data2.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file [1] + label_suffix, 'rb') as fp:
#         target_subject_labels2 = pickle.load(fp)
#     target_subject_data2, target_subject_labels2 = shuffle_data(target_subject_data2, target_subject_labels2)
#     # 加载data和labels：
#     dataset_dir = "E:/1_myresearch/Code/Trial/EEG_data/SEED_DE_session3_9_6/"
#     with open(dataset_dir + data_file [2] + cnn_suffix, 'rb') as fp:
#         target_subject_data3 = pickle.load(fp)
#         target_subject_data3 = np.transpose(target_subject_data3.reshape(-1, 310, 1, 1), (0, 3, 2, 1))
#     with open(dataset_dir + data_file [2] + label_suffix, 'rb') as fp:
#         target_subject_labels3 = pickle.load(fp)
#     target_subject_data3, target_subject_labels3 = shuffle_data(target_subject_data3, target_subject_labels3)
#
#     source_data = np.concatenate([source_subject_data1, target_subject_data1, source_subject_data2, target_subject_data2], axis=0)  # 沿着第0维度拼接
#     source_labels = np.concatenate([source_subject_labels1, target_subject_labels1, source_subject_labels2, target_subject_labels2], axis=0)  # 沿着第0维度拼接
#
#     target_data = np.concatenate([source_subject_data3, target_subject_data3], axis=0)  # 沿着第0维度拼接
#     target_labels = np.concatenate([source_subject_labels3, target_subject_labels3],axis=0)  # 沿着第0维度拼接
#
#     return source_data, source_labels, target_data, target_labels