{".class": "MypyFile", "_fullname": "memory_config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BatchProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "memory_config.BatchProcessor", "name": "BatchProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "memory_config.BatchProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "memory_config", "mro": ["memory_config.BatchProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.BatchProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "batch_size"], "arg_types": ["memory_config.BatchProcessor", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.BatchProcessor.batch_size", "name": "batch_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.BatchProcessor.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "data", "process_func", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.BatchProcessor.process_batches", "name": "process_batches", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "memory_config.BatchProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "memory_config.BatchProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MEMORY_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "memory_config.MEMORY_CONFIG", "name": "MEMORY_CONFIG", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "MemoryConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "memory_config.MemoryConfig", "name": "MemoryConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "memory_config.MemoryConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "memory_config", "mro": ["memory_config.MemoryConfig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "gpu_memory_gb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.MemoryConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "gpu_memory_gb"], "arg_types": ["memory_config.MemoryConfig", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adaptive_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "base_batch_size", "data_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.MemoryConfig.adaptive_batch_size", "name": "adaptive_batch_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "base_batch_size", "data_size"], "arg_types": ["memory_config.MemoryConfig", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adaptive_batch_size of MemoryConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "memory_config.MemoryConfig.auto_config", "name": "auto_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "memory_config.MemoryConfig.auto_config", "name": "auto_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "memory_config.MemoryConfig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto_config of MemoryConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "check_memory_pressure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.MemoryConfig.check_memory_pressure", "name": "check_memory_pressure", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "threshold"], "arg_types": ["memory_config.MemoryConfig", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_memory_pressure of MemoryConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_cache_frequency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.MemoryConfig.clear_cache_frequency", "name": "clear_cache_frequency", "type": "builtins.int"}}, "clear_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.MemoryConfig.clear_memory", "name": "clear_memory", "type": null}}, "enable_memory_monitoring": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.MemoryConfig.enable_memory_monitoring", "name": "enable_memory_monitoring", "type": "builtins.bool"}}, "eval_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.MemoryConfig.eval_batch_size", "name": "eval_batch_size", "type": "builtins.int"}}, "get_gpu_memory_gb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "memory_config.MemoryConfig.get_gpu_memory_gb", "name": "get_gpu_memory_gb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "memory_config.MemoryConfig.get_gpu_memory_gb", "name": "get_gpu_memory_gb", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_gpu_memory_gb of MemoryConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gpu_memory_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.MemoryConfig.gpu_memory_gb", "name": "gpu_memory_gb", "type": "builtins.float"}}, "mia_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.MemoryConfig.mia_batch_size", "name": "mia_batch_size", "type": "builtins.int"}}, "print_memory_usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.MemoryConfig.print_memory_usage", "name": "print_memory_usage", "type": null}}, "unlearn_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "memory_config.MemoryConfig.unlearn_batch_size", "name": "unlearn_batch_size", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "memory_config.MemoryConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "memory_config.MemoryConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "memory_config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "memory_config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "memory_config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "memory_config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "memory_config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "memory_config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_memory_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.get_memory_config", "name": "get_memory_config", "type": null}}, "memory_optimized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["clear_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.memory_optimized", "name": "memory_optimized", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "update_forgetting_scheduler_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scheduler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "memory_config.update_forgetting_scheduler_config", "name": "update_forgetting_scheduler_config", "type": null}}}, "path": "D:\\continual_learning\\CL_ER\\memory_config.py"}