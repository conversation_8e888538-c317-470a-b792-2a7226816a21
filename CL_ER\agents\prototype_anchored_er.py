import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
from torch.utils import data
from utils.buffer.buffer import Buffer
from agents.base import ContinualLearner
from continuum.data_utils import dataset_transform
from utils.setup_elements import transforms_match
from utils.utils import maybe_cuda, AverageMeter
from agents.exp_replay import ExperienceReplay
from utils.io import save_model


class PA_loss(object):
    """原型锚定损失计算类"""
    def __init__(self, params):
        super().__init__()
        self.ema = 0.5  # 指数移动平均系数
        # 初始化三类情绪的原型 (针对SEED数据集的3种情绪状态)
        # 特征维度设置为64，如果模型输出维度不同，会在使用时自动调整
        self.feature_dim = 64
        self.class_prototypes = {cls: torch.randn(1, self.feature_dim) for cls in [0, 1, 2]}
        self.num_classes = 3  # 默认为SEED数据集的3个类别
        
    def update_prototypes(self, features, labels, device):
        """更新每个类别的原型"""
        # 更新特征维度（如果需要）
        if features.size(1) != self.feature_dim:
            self.feature_dim = features.size(1)
            # 重新初始化原型，保持与特征相同的维度
            self.class_prototypes = {cls: torch.randn(1, self.feature_dim).to(device) 
                                  for cls in self.class_prototypes.keys()}
        
        # 获取数据中的类别数量
        unique_labels = torch.unique(labels)
        self.num_classes = max(self.num_classes, int(unique_labels.max().item()) + 1)
        
        # 确保所有类别都有原型
        for cls in range(self.num_classes):
            if cls not in self.class_prototypes:
                self.class_prototypes[cls] = torch.randn(1, self.feature_dim).to(device)
        
        # 为每个类收集样本
        class_prototypes = {}
        cls_samples = {cls: [] for cls in range(self.num_classes)}
        for feature, label in zip(features, labels):
            label_idx = label.item()
            if label_idx in cls_samples:
                cls_samples[label_idx].append(feature.view(1, -1))
        
        # 计算每个类的原型
        for cls, samples in cls_samples.items():
            if not samples:
                continue
            class_prototypes[cls] = torch.mean(torch.stack(samples), dim=0)
        
        # 使用指数移动平均更新原型
        for cls, prototype in class_prototypes.items():
            if cls in self.class_prototypes:
                new_prototypes = self.ema * prototype + (1 - self.ema) * self.class_prototypes[cls].to(device)
                class_prototypes[cls] = new_prototypes.detach()
            else:
                class_prototypes[cls] = prototype.detach()
        
        # 更新原型字典
        self.class_prototypes.update(class_prototypes)
        return class_prototypes
        
    def get_pa_loss(self, combined_features, mem_y, batch_y, criterion, device):
        """计算原型锚定损失"""
        # 提取记忆样本和当前样本的特征
        mem_features = combined_features[batch_y.size(0):, :]
        mem_features = mem_features / (mem_features.norm(dim=1, keepdim=True) + 1e-8)  # L2规范化
        data_features = combined_features[:batch_y.size(0), :]
        data_features = data_features / (data_features.norm(dim=1, keepdim=True) + 1e-8)
        
        # 更新原型
        class_prototypes = self.update_prototypes(mem_features, mem_y, device)
        
        # 构建原型向量，确保包含所有可能的类别
        prototype_list = []
        for cls in range(self.num_classes):
            if cls in class_prototypes:
                prototype_list.append(class_prototypes[cls])
            elif cls in self.class_prototypes:
                prototype_list.append(self.class_prototypes[cls].to(device))
            else:
                # 如果没有这个类的原型，创建一个随机的
                random_prototype = torch.randn(1, self.feature_dim).to(device)
                self.class_prototypes[cls] = random_prototype
                prototype_list.append(random_prototype)
        
        if not prototype_list:  # 如果没有有效的原型，返回零损失
            return torch.tensor(0.0).to(device), torch.tensor(0.0).to(device)
            
        prototype = torch.cat(prototype_list, dim=0)
        prototype = prototype / (prototype.norm(dim=1, keepdim=True) + 1e-8)
        
        # 计算样本与原型间的相似度
        similarities_data = torch.matmul(data_features, prototype.T)  # [batch_size, n_classes]
        similarities_mem = torch.matmul(mem_features, prototype.T)  # [mem_size, n_classes]
        
        # 确保标签在有效范围内
        max_label = max(batch_y.max().item(), mem_y.max().item())
        if max_label >= similarities_data.size(1):
            print(f"警告: 标签值 {max_label} 超出了相似度矩阵的维度 {similarities_data.size(1)}，将添加额外的原型")
            # 如果有标签超出了当前原型数量，扩展相似度矩阵
            pad_size = max_label + 1 - similarities_data.size(1)
            pad_data = torch.zeros(similarities_data.size(0), pad_size).to(device)
            pad_mem = torch.zeros(similarities_mem.size(0), pad_size).to(device)
            similarities_data = torch.cat([similarities_data, pad_data], dim=1)
            similarities_mem = torch.cat([similarities_mem, pad_mem], dim=1)
            
            # 更新类别数量
            self.num_classes = max_label + 1
        
        # 计算损失
        pa_mem_loss = criterion(similarities_mem, mem_y)
        pa_data_loss = criterion(similarities_data, batch_y)
        
        return pa_data_loss, pa_mem_loss


class PrototypeAnchoredER(ExperienceReplay):
    """原型锚定经验回放(PAER)方法"""
    def __init__(self, models, opt, params):
        """初始化"""
        # 先初始化基类，这会创建buffer
        super(PrototypeAnchoredER, self).__init__(models, opt, params)

        # 为缓冲区传递适当的模型，确保兼容性
        if self.model_ecoder is not None and self.model_Cls is not None:
            self.buffer = Buffer(self.model_ecoder, self.model_Cls, params)
        else:
            self.buffer = Buffer(self.model, None, params)  # 适配旧接口

        self.mem_size = params.mem_size
        self.eps_mem_batch = params.eps_mem_batch
        self.mem_iters = params.mem_iters
        
        self.pa_loss = PA_loss(params)
        self.prototype_weight = 1.0
        if hasattr(params, 'prototype_weight'):
            self.prototype_weight = params.prototype_weight
        elif hasattr(params, 'cm_weight'):  # 兼容旧参数名
            self.prototype_weight = params.cm_weight
        print(f"初始化原型锚定经验回放(PAER)，原型损失权重: {self.prototype_weight}")
        
        # 检测特征维度
        print("注意：特征维度将在第一批数据处理时自动检测并调整")
        print("如果您想提前知道特征维度，可以运行 python check_model_dims.py")
        
        # 添加保存路径
        self.save_dir = params.save_path if hasattr(params, 'save_path') and params.save_path is not None else './output/models'
        os.makedirs(self.save_dir, exist_ok=True)
        
        # 添加任务ID追踪
        self.tasks = []  # 用于记录每个样本的任务ID
        self.buffer.register_buffer('tasks', torch.LongTensor(self.mem_size).fill_(-1))
        
    def train_learner(self, x_train, y_train, task_id=None):
        """训练模型并保存结果
        
        Args:
            x_train: 训练数据
            y_train: 训练标签
            task_id: 当前任务ID，用于记录在缓冲区中
        """
        self.before_train(x_train, y_train)
        
        # 保存当前任务的样本，用于后续可能的缓冲区平衡
        if task_id is not None:
            if not hasattr(self, 'task_samples'):
                self.task_samples = {}
            # 只在首次遇到该任务时保存样本，避免重复保存
            if task_id not in self.task_samples:
                # 为了节省内存，我们只保存一部分样本（最多500个）
                max_samples = min(500, len(x_train))
                if isinstance(x_train, np.ndarray):
                    # 随机选择样本
                    indices = np.random.choice(len(x_train), max_samples, replace=False)
                    self.task_samples[task_id] = {
                        'x': x_train[indices],
                        'y': y_train[indices]
                    }
                else:
                    # 如果已经是PyTorch张量
                    indices = torch.randperm(len(x_train))[:max_samples]
                    self.task_samples[task_id] = {
                        'x': x_train[indices],
                        'y': y_train[indices]
                    }
                print(f"已保存任务 {task_id} 的 {max_samples} 个样本用于缓冲区平衡")
        
        # 设置数据加载器
        train_dataset = dataset_transform(x_train, y_train, transform=transforms_match[self.data])
        train_loader = data.DataLoader(train_dataset, batch_size=self.batch, shuffle=True, num_workers=0,
                                       drop_last=True)
        # 设置模型
        if self.model_ecoder is not None and self.model_Cls is not None:
            self.model_ecoder.train()
            self.model_Cls.train()
        else:
            self.model.train()

        # 设置追踪器
        losses_batch = AverageMeter()
        losses_mem = AverageMeter()
        losses_pa = AverageMeter()  # 跟踪原型锚定损失
        acc_batch = AverageMeter()
        acc_mem = AverageMeter()
        
        device = "cuda" if self.cuda else "cpu"
        
        # 分析数据集中的类别数量
        try:
            # 检查是否为NumPy数组并转换为PyTorch张量
            if isinstance(y_train, np.ndarray):
                unique_labels = np.unique(y_train)
                num_classes = len(unique_labels)
                print(f"数据集中检测到 {num_classes} 个类别: {unique_labels.tolist()}")
                self.pa_loss.num_classes = max(self.pa_loss.num_classes, num_classes)
            else:
                unique_labels = torch.unique(y_train)
                num_classes = len(unique_labels)
                print(f"数据集中检测到 {num_classes} 个类别: {unique_labels.tolist()}")
                self.pa_loss.num_classes = max(self.pa_loss.num_classes, num_classes)
        except Exception as e:
            print(f"分析数据集类别时出错: {e}")
            print("继续使用默认类别数量")
        
        for ep in range(self.epoch):
            for i, batch_data in enumerate(train_loader):
                # 批次更新
                batch_x, batch_y = batch_data
                batch_x = maybe_cuda(batch_x, self.cuda)
                batch_y = maybe_cuda(batch_y, self.cuda)
                
                for j in range(self.mem_iters):
                    if self.task_seen == 0:
                        # 第一个任务直接训练（没有经验回放）
                        # 提取特征和计算logits
                        if self.model_ecoder is not None and self.model_Cls is not None:
                            features = self.model_ecoder(batch_x)
                            # 打印特征维度
                            if ep == 0 and i == 0:
                                print(f"特征维度: {features.shape}")
                            logits = self.model_Cls(features)
                        else:
                            features = self.model(batch_x)
                            # 打印特征维度
                            if ep == 0 and i == 0:
                                print(f"特征维度: {features.shape}")
                            logits = features
                            
                        loss = self.criterion(logits, batch_y)
                        
                        # 更新跟踪器
                        _, pred_label = torch.max(logits, 1)
                        correct_cnt = (pred_label == batch_y).sum().item() / batch_y.size(0)
                        acc_batch.update(correct_cnt, batch_y.size(0))
                        losses_batch.update(loss.item(), batch_y.size(0))
                        
                        # 反向传播
                        self.opt.zero_grad()
                        loss.backward()
                        self.opt.step()
                    
                    elif self.task_seen > 0:
                        # 经验回放 + 原型锚定
                        # mem_x, mem_y = self.buffer.retrieve()
                        mem_x, mem_y = self.buffer.retrieve(x=batch_x, y=batch_y)
                        if mem_x.size(0) > 0:
                            mem_x = maybe_cuda(mem_x, self.cuda)
                            mem_y = maybe_cuda(mem_y, self.cuda)
                            
                            # 连接当前批次和记忆样本
                            combined_x = torch.cat((batch_x, mem_x))
                            combined_y = torch.cat((batch_y, mem_y))
                            
                            # 提取特征和计算logits
                            if self.model_ecoder is not None and self.model_Cls is not None:
                                combined_features = self.model_ecoder(combined_x)
                                combined_logits = self.model_Cls(combined_features)
                            else:
                                combined_features = self.model(combined_x)
                                combined_logits = combined_features
                            
                            # 标准交叉熵损失
                            ce_loss = self.criterion(combined_logits, combined_y)
                            
                            # 原型锚定损失
                            try:
                                pa_data_loss, pa_mem_loss = self.pa_loss.get_pa_loss(
                                    combined_features, mem_y, batch_y, self.criterion, device
                                )
                                pa_loss = pa_data_loss + pa_mem_loss
                                
                                # 总损失
                                total_loss = ce_loss + self.prototype_weight * pa_loss
                                
                                # 更新跟踪器
                                losses_pa.update(pa_loss.item(), combined_y.size(0))
                            except Exception as e:
                                print(f"计算原型锚定损失时出错: {e}")
                                print(f"使用仅交叉熵损失继续训练")
                                total_loss = ce_loss
                                losses_pa.update(0, combined_y.size(0))
                            
                            losses_mem.update(ce_loss.item(), combined_y.size(0))
                            _, pred_label = torch.max(combined_logits, 1)
                            correct_cnt = (pred_label == combined_y).sum().item() / combined_y.size(0)
                            acc_mem.update(correct_cnt, combined_y.size(0))
                            
                            # 反向传播
                            self.opt.zero_grad()
                            total_loss.backward()
                            self.opt.step()
                
            # 输出训练信息
            if self.task_seen == 0:
                print('-----epoch: {}/{} -----task: {} -----loss: {:.6f} -----running train acc: {:.3f}'.format(
                    ep+1, self.epoch, self.task_seen, losses_batch.avg(), acc_batch.avg()))
            if self.task_seen > 0:
                print('-----epoch: {}/{} -----task: {} -----loss: {:.6f} -----pa_loss: {:.6f} -----running train acc: {:.3f}'.format(
                    ep+1, self.epoch, self.task_seen, losses_mem.avg(), losses_pa.avg(), acc_mem.avg()))
            
            # 在每个epoch结束后更新一次缓冲区
            # 这里我们使用整个训练集更新，而不是每个批次都更新
            if task_id is not None:
                self.update_buffer_with_task_id(x_train, y_train, task_id)
                print(f"Epoch {ep+1}/{self.epoch} 结束，使用任务ID {task_id} 更新缓冲区")
            else:
                self.update_buffer_with_task_id(x_train, y_train, self.task_seen)
                print(f"Epoch {ep+1}/{self.epoch} 结束，使用任务ID {self.task_seen} 更新缓冲区")
            
            # 分析缓冲区分布
            self.analyze_buffer_distribution()
        
        # 训练后处理
        self.after_train()
        
        # 保存模型和缓冲区
        self.save_model_and_buffer(task_id)
    
    def update_buffer_with_task_id(self, x, y, task_id):
        """更新缓冲区并记录任务ID
        
        Args:
            x: 输入数据
            y: 标签
            task_id: 任务ID
        """
        # 确保x和y是PyTorch张量
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x).float()
        if isinstance(y, np.ndarray):
            y = torch.from_numpy(y).long()

        # 确保数据在正确的设备上 - 关键修复
        device = next(self.model_ecoder.parameters()).device if self.model_ecoder is not None else next(self.model.parameters()).device
        x = x.to(device)
        y = y.to(device)

        # 获取当前缓冲区索引
        current_index = self.buffer.current_index
        n_seen = self.buffer.n_seen_so_far
        
        # 跟踪每个任务的样本总数（只在首次遇到该任务时记录）
        if not hasattr(self, 'task_total_samples'):
            self.task_total_samples = {}
        
        # 只在首次遇到该任务时记录样本总数，避免多个epoch重复累加
        if task_id not in self.task_total_samples:
            self.task_total_samples[task_id] = len(x)
            print(f"任务 {task_id} 首次记录样本数: {self.task_total_samples[task_id]}")
        
        # 计算每个任务在缓冲区中的最小样本数要求（5%）
        min_samples_required = {}
        for t_id, total in self.task_total_samples.items():
            min_samples_required[t_id] = max(int(total * 0.05), 10)  # 至少10个样本或5%
            
        # 检查缓冲区中每个任务的当前样本数
        if hasattr(self.buffer, 'tasks') and self.buffer.current_index > 0:
            valid_tasks = self.buffer.tasks[:self.buffer.current_index]
            task_counts = {}
            for t_id in self.task_total_samples.keys():
                task_counts[t_id] = (valid_tasks == t_id).sum().item()
                print(f"缓冲区中任务 {t_id} 当前有 {task_counts[t_id]} 个样本，最低要求 {min_samples_required[t_id]} 个样本")
        
        # 更新缓冲区
        updated_indices = self.buffer.update(x, y)
        
        # 打印日志
        print(f"更新缓冲区: 添加/更新了 {len(updated_indices)} 个样本，任务ID: {task_id}")
        
        # 记录任务ID
        # 1. 对于新更新的索引，设置正确的任务ID
        if updated_indices and len(updated_indices) > 0:
            for idx in updated_indices:
                self.buffer.tasks[idx] = task_id
        
        # 2. 兼容旧方法：计算新样本被添加到缓冲区的位置
        new_current_index = self.buffer.current_index
        
        # 如果缓冲区是循环使用的，需要特殊处理
        if new_current_index < current_index:  # 说明缓冲区已经循环
            # 先更新从current_index到缓冲区末尾的部分
            end_count = self.buffer.buffer_img.size(0) - current_index
            if end_count > 0:
                self.buffer.tasks[current_index:] = task_id
            
            # 再更新从开始到new_current_index的部分
            if new_current_index > 0:
                self.buffer.tasks[:new_current_index] = task_id
        else:
            # 正常情况，直接更新
            if new_current_index > current_index:
                self.buffer.tasks[current_index:new_current_index] = task_id
        
        # 检查是否有未赋值的任务ID（仍然为-1）
        if torch.any(self.buffer.tasks[:self.buffer.current_index] == -1):
            # 将所有-1的任务ID更新为当前任务ID
            mask = (self.buffer.tasks[:self.buffer.current_index] == -1)
            num_fixed = mask.sum().item()
            if num_fixed > 0:
                self.buffer.tasks[:self.buffer.current_index][mask] = task_id
                print(f"修复了 {num_fixed} 个未分配任务ID的样本，设置为任务 {task_id}")
        
        # 检查每个任务的样本数是否满足最小要求
        if hasattr(self.buffer, 'tasks') and self.buffer.current_index > 0:
            valid_tasks = self.buffer.tasks[:self.buffer.current_index]
            task_counts = {}
            for t_id in self.task_total_samples.keys():
                if t_id != task_id:  # 不检查当前正在更新的任务
                    task_counts[t_id] = (valid_tasks == t_id).sum().item()
                    if task_counts[t_id] < min_samples_required[t_id]:
                        print(f"警告: 任务 {t_id} 在缓冲区中只有 {task_counts[t_id]} 个样本，少于最小要求 {min_samples_required[t_id]}")
                        
                        # 如果某个任务的样本数低于最小要求，我们需要从当前任务中腾出空间
                        # 这里我们可以实现一个简单的策略：从当前任务中随机选择一些样本替换
                        samples_needed = min_samples_required[t_id] - task_counts[t_id]
                        if samples_needed > 0 and task_id in self.task_total_samples:
                            # 找出当前任务在缓冲区中的样本索引
                            current_task_indices = (valid_tasks == task_id).nonzero().squeeze(-1)
                            
                            if len(current_task_indices) > samples_needed:
                                # 随机选择一些当前任务的样本进行替换
                                replace_indices = current_task_indices[torch.randperm(len(current_task_indices))[:samples_needed]]
                                
                                # 从任务t_id的所有样本中选择一些添加到缓冲区
                                # 这里我们需要访问任务t_id的样本，但我们可能没有保存这些样本
                                # 作为一个简单的解决方案，我们可以在训练过程中保存每个任务的一些样本
                                if hasattr(self, 'task_samples') and t_id in self.task_samples:
                                    t_x = self.task_samples[t_id]['x']
                                    t_y = self.task_samples[t_id]['y']
                                    
                                    # 确保t_x和t_y是PyTorch张量
                                    if isinstance(t_x, np.ndarray):
                                        t_x = torch.from_numpy(t_x).float()
                                    if isinstance(t_y, np.ndarray):
                                        t_y = torch.from_numpy(t_y).long()
                                    
                                    # 随机选择一些样本
                                    t_indices = torch.randperm(len(t_x))[:samples_needed]
                                    t_x_selected = t_x[t_indices]
                                    t_y_selected = t_y[t_indices]
                                    
                                    # 替换缓冲区中的样本
                                    for i, idx in enumerate(replace_indices):
                                        self.buffer.buffer_img[idx] = t_x_selected[i]
                                        self.buffer.buffer_label[idx] = t_y_selected[i]
                                        self.buffer.tasks[idx] = t_id
                                    
                                    print(f"已从任务 {task_id} 中腾出 {len(replace_indices)} 个样本位置，添加任务 {t_id} 的样本")
        
        # 返回更新后的索引
        return updated_indices
    
    def save_model_and_buffer(self, task_id=None):
        """保存模型和缓冲区
        
        Args:
            task_id: 当前任务ID，用于命名保存的文件
        """
        # 创建保存目录
        os.makedirs(self.save_dir, exist_ok=True)
        
        # 获取当前时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 获取代理名称
        agent_name = self.__class__.__name__
        
        # 获取参数信息
        param_info = ""
        if hasattr(self, 'params'):
            # 添加一些关键参数信息
            if hasattr(self.params, 'agent'):
                param_info += f"_{self.params.agent}"
            if hasattr(self.params, 'update'):
                param_info += f"_{self.params.update}"
            if hasattr(self.params, 'retrieve'):
                param_info += f"_{self.params.retrieve}"
        
        # 构建保存路径
        task_suffix = f"_task{task_id}" if task_id is not None else ""
        model_save_path = os.path.join(self.save_dir, f"{agent_name}{param_info}{task_suffix}_{timestamp}_model.pth")
        buffer_save_path = os.path.join(self.save_dir, f"{agent_name}{param_info}{task_suffix}_{timestamp}_buffer.pth")
        
        # 保存模型
        if self.model_ecoder is not None and self.model_Cls is not None:
            state_dict = {
                'encoder': self.model_ecoder.state_dict(),
                'classifier': self.model_Cls.state_dict(),
                'pa_loss': self.pa_loss.class_prototypes,
                'feature_dim': self.pa_loss.feature_dim,
                'num_classes': self.pa_loss.num_classes,
                'task_seen': self.task_seen
            }
        else:
            state_dict = {
                'model': self.model.state_dict(),
                'pa_loss': self.pa_loss.class_prototypes,
                'feature_dim': self.pa_loss.feature_dim,
                'num_classes': self.pa_loss.num_classes,
                'task_seen': self.task_seen
            }
        
        # 保存优化器状态
        state_dict['optimizer'] = self.opt.state_dict()
        
        # 保存任务总样本数信息
        if hasattr(self, 'task_total_samples'):
            state_dict['task_total_samples'] = self.task_total_samples
        
        # 保存模型
        torch.save(state_dict, model_save_path)
        print(f"模型已保存到 {model_save_path}")
        
        # 保存缓冲区
        buffer_state = {
            'buffer_img': self.buffer.buffer_img,
            'buffer_label': self.buffer.buffer_label,
            'current_index': self.buffer.current_index,
            'n_seen_so_far': self.buffer.n_seen_so_far,
            'tasks': self.buffer.tasks
        }
        torch.save(buffer_state, buffer_save_path)
        print(f"缓冲区已保存到 {buffer_save_path}")
        
        # 同时保存一个最新版本的文件（方便后续加载）
        latest_model_path = os.path.join(self.save_dir, f"latest_model.pth")
        latest_buffer_path = os.path.join(self.save_dir, f"latest_buffer.pth")
        torch.save(state_dict, latest_model_path)
        torch.save(buffer_state, latest_buffer_path)
        print(f"最新模型和缓冲区已保存到 {latest_model_path} 和 {latest_buffer_path}")
    
    def load_model_and_buffer(self, model_path, buffer_path=None):
        """加载模型和缓冲区
        
        Args:
            model_path: 模型保存路径
            buffer_path: 缓冲区保存路径，如果为None则不加载缓冲区
        """
        # 加载模型
        if not os.path.exists(model_path):
            print(f"警告: 模型文件 {model_path} 不存在")
            return False
        
        try:
            state_dict = torch.load(model_path, map_location=torch.device('cuda' if self.cuda else 'cpu'))
            
            # 加载模型参数
            if 'encoder' in state_dict and 'classifier' in state_dict:
                if self.model_ecoder is not None and self.model_Cls is not None:
                    self.model_ecoder.load_state_dict(state_dict['encoder'])
                    self.model_Cls.load_state_dict(state_dict['classifier'])
                else:
                    print("警告: 模型结构不匹配，无法加载编码器和分类器")
                    return False
            elif 'model' in state_dict:
                if self.model is not None:
                    self.model.load_state_dict(state_dict['model'])
                else:
                    print("警告: 模型结构不匹配，无法加载模型")
                    return False
            
            # 加载PA损失状态
            if 'pa_loss' in state_dict:
                self.pa_loss.class_prototypes = state_dict['pa_loss']
            if 'feature_dim' in state_dict:
                self.pa_loss.feature_dim = state_dict['feature_dim']
            if 'num_classes' in state_dict:
                self.pa_loss.num_classes = state_dict['num_classes']
            
            # 加载任务计数
            if 'task_seen' in state_dict:
                self.task_seen = state_dict['task_seen']
            
            # 加载优化器状态
            if 'optimizer' in state_dict:
                self.opt.load_state_dict(state_dict['optimizer'])
            
            print(f"模型已从 {model_path} 加载")
        except Exception as e:
            print(f"加载模型时出错: {e}")
            return False
        
        # 加载缓冲区
        if buffer_path is not None and os.path.exists(buffer_path):
            try:
                buffer_state = torch.load(buffer_path, map_location=torch.device('cuda' if self.cuda else 'cpu'))
                
                # 加载缓冲区数据
                self.buffer.buffer_img.copy_(buffer_state['buffer_img'])
                self.buffer.buffer_label.copy_(buffer_state['buffer_label'])
                self.buffer.current_index = buffer_state['current_index']
                self.buffer.n_seen_so_far = buffer_state['n_seen_so_far']
                
                # 加载任务ID
                if 'tasks' in buffer_state:
                    self.buffer.tasks.copy_(buffer_state['tasks'])
                
                print(f"缓冲区已从 {buffer_path} 加载，当前包含 {self.buffer.current_index} 个样本")
            except Exception as e:
                print(f"加载缓冲区时出错: {e}")
                return False
        
        return True
    
    def get_task_data(self, task_id):
        """获取指定任务的数据，用于遗忘
        
        Args:
            task_id: 要获取的任务ID
            
        Returns:
            task_x: 任务输入数据
            task_y: 任务标签
            task_ids: 任务ID列表
        """
        # 检查缓冲区是否为空
        if self.buffer.current_index == 0:
            print(f"警告: 缓冲区为空，无法获取任务 {task_id} 的数据")
            return torch.tensor([]), torch.tensor([]), torch.tensor([])
        
        # 获取任务ID
        tasks = self.buffer.tasks[:self.buffer.current_index]
        
        # 找出指定任务的样本索引
        task_indices = []
        for i in range(len(tasks)):
            if tasks[i].item() == task_id:
                task_indices.append(i)
        
        if not task_indices:
            print(f"未找到任务 {task_id} 的数据")
            return torch.tensor([]), torch.tensor([]), torch.tensor([])
        
        # 提取任务数据
        task_idx_tensor = torch.tensor(task_indices, dtype=torch.long, device=self.buffer.buffer_img.device)
        task_x = self.buffer.buffer_img[task_idx_tensor]
        task_y = self.buffer.buffer_label[task_idx_tensor]
        task_ids = tasks[task_idx_tensor]
        
        print(f"成功提取任务 {task_id} 的数据，共 {len(task_indices)} 个样本")
        
        return task_x, task_y, task_ids
    
    def forget_task(self, forget_task_id, mode='dual_teacher'):
        """遗忘指定任务
        
        Args:
            forget_task_id: 要遗忘的任务ID
            mode: 遗忘模式，可选 'dual_teacher' 或 'adversarial'
            
        Returns:
            results: 遗忘结果
        """
        try:
            # 导入遗忘代理
            from agents.advanced_unlearning import EnhancedUnlearningAgent
            
            # 创建遗忘代理
            if self.model_ecoder is not None and self.model_Cls is not None:
                models = [self.model_ecoder, self.model_Cls]
            else:
                models = self.model
                
            unlearning_agent = EnhancedUnlearningAgent(models, self.opt, self.params)
            
            # 设置缓冲区
            unlearning_agent.buffer = self.buffer
            
            # 执行遗忘
            results = unlearning_agent.forget(forget_task_id, mode=mode)
            
            # 更新模型参数（遗忘后的模型已经在unlearning_agent中更新）
            # 由于我们直接传递了模型引用，所以不需要再次更新
            
            print(f"任务 {forget_task_id} 遗忘完成")
            print(f"遗忘前准确率: {results['pre_forget_accuracy']:.4f}, 遗忘后准确率: {results['post_forget_accuracy']:.4f}")
            print(f"保留任务准确率变化: {results['retain_accuracy_change']:.4f}")
            
            # 保存遗忘后的模型和缓冲区
            self.save_model_and_buffer(task_id=f"after_forget_{forget_task_id}")
            
            return results
            
        except Exception as e:
            print(f"遗忘任务时出错: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e), "success": False} 

    def analyze_buffer_distribution(self):
        """分析缓冲区中各任务的样本分布"""
        if not hasattr(self.buffer, 'tasks') or not hasattr(self.buffer, 'current_index'):
            print("缓冲区没有任务ID信息或当前索引信息，无法分析分布")
            return
        
        # 获取有效的任务ID
        valid_tasks = self.buffer.tasks[:self.buffer.current_index]
        if len(valid_tasks) == 0:
            print("缓冲区为空，无法分析分布")
            return
        
        # 统计每个任务的样本数
        unique_tasks, counts = torch.unique(valid_tasks, return_counts=True)
        unique_tasks = unique_tasks.cpu().numpy()
        counts = counts.cpu().numpy()
        
        print("\n===== 缓冲区任务分布分析 =====")
        print(f"缓冲区大小: {self.buffer.buffer_img.size(0)}")
        print(f"当前使用: {self.buffer.current_index} 个样本")
        
        # 计算每个任务的占比
        total_samples = self.buffer.current_index
        print("\n任务ID\t样本数\t占比\t最低要求\t状态")
        print("-" * 50)
        
        for i, task_id in enumerate(unique_tasks):
            task_id_int = int(task_id)
            count = counts[i]
            percentage = count / total_samples * 100
            
            # 计算最低要求
            min_required = 0
            if hasattr(self, 'task_total_samples') and task_id_int in self.task_total_samples:
                task_total = self.task_total_samples[task_id_int]
                min_required = max(int(task_total * 0.05), 10)  # 至少10个样本或5%
            
            # 判断状态
            if min_required > 0:
                if count >= min_required:
                    status = "✓ 满足"
                else:
                    status = f"✗ 不足 (缺 {min_required - count})"
            else:
                status = "- 未知要求"
            
            print(f"{task_id_int}\t{count}\t{percentage:.1f}%\t{min_required}\t{status}")
        
        print("-" * 50)
        
        # 检查是否有任务完全缺失
        if hasattr(self, 'task_total_samples'):
            missing_tasks = []
            for task_id in self.task_total_samples.keys():
                if task_id not in unique_tasks:
                    missing_tasks.append(task_id)
            
            if missing_tasks:
                print(f"\n警告: 以下任务在缓冲区中完全缺失: {missing_tasks}")
        
        return unique_tasks, counts 