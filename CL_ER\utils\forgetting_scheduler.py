"""
遗忘调度器模块
实现正确的遗忘学习模式：在指定任务训练完成后立即执行遗忘
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import copy
from sklearn.metrics import roc_auc_score
import gc
import sys
import os

# 添加当前目录到路径以导入memory_config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from memory_config import get_memory_config, memory_optimized
except ImportError:
    # 如果导入失败，使用默认配置
    def get_memory_config():
        class DefaultConfig:
            eval_batch_size = 16
            unlearn_batch_size = 8
            mia_batch_size = 16
        return DefaultConfig()

    def memory_optimized(clear_cache=True):
        def decorator(func):
            return func
        return decorator


class ForgettingScheduler:
    """
    遗忘调度器类
    
    负责管理遗忘任务列表、遗忘时机和遗忘执行
    实现论文中描述的正确遗忘模式：
    1. 指定要遗忘的任务列表
    2. 在指定任务训练完成后立即执行遗忘
    3. 遗忘后继续训练后续任务
    """
    
    def __init__(self, forget_task_ids: List[int], total_tasks: int,
                 unlearning_epochs: int = 20, verbose: bool = True,
                 unlearning_method: str = "hybrid", remove_from_buffer: bool = True):
        """
        初始化遗忘调度器

        Args:
            forget_task_ids: 要遗忘的任务ID列表
            total_tasks: 总任务数
            unlearning_epochs: 遗忘训练轮数
            verbose: 是否打印详细信息
            unlearning_method: 遗忘方法 ("entropy_maximization", "confidence_reduction",
                             "label_smoothing", "adversarial", "hybrid")
            remove_from_buffer: 是否从缓冲区中移除遗忘任务的样本
        """
        self.forget_task_ids = set(forget_task_ids) if forget_task_ids else set()
        self.total_tasks = total_tasks
        self.unlearning_epochs = unlearning_epochs
        self.verbose = verbose
        self.unlearning_method = unlearning_method
        self.remove_from_buffer = remove_from_buffer

        # 遗忘状态跟踪
        self.forgotten_tasks = set()  # 已遗忘的任务
        self.current_task = -1  # 当前训练的任务ID

        # 遗忘效果记录
        self.forgetting_results = {}  # 存储每次遗忘的结果

        # 内存配置
        self.memory_config = get_memory_config()

        if self.verbose:
            print(f"遗忘调度器初始化完成")
            print(f"要遗忘的任务: {sorted(list(self.forget_task_ids))}")
            print(f"总任务数: {total_tasks}")
            print(f"遗忘方法: {self.unlearning_method}")
            print(f"内存配置: 评估批大小={self.memory_config.eval_batch_size}, 遗忘批大小={self.memory_config.unlearn_batch_size}")
    
    def should_forget_after_task(self, task_id: int) -> bool:
        """
        检查在指定任务训练完成后是否需要执行遗忘
        
        Args:
            task_id: 刚完成训练的任务ID
            
        Returns:
            bool: 是否需要执行遗忘
        """
        self.current_task = task_id
        
        # 检查是否需要遗忘刚训练完的任务
        if task_id in self.forget_task_ids and task_id not in self.forgotten_tasks:
            return True
        
        return False
    
    def get_tasks_to_forget(self, completed_task_id: int) -> List[int]:
        """
        获取需要遗忘的任务列表
        
        Args:
            completed_task_id: 刚完成训练的任务ID
            
        Returns:
            List[int]: 需要遗忘的任务ID列表
        """
        tasks_to_forget = []
        
        # 检查是否需要遗忘刚完成的任务
        if completed_task_id in self.forget_task_ids and completed_task_id not in self.forgotten_tasks:
            tasks_to_forget.append(completed_task_id)
        
        # 可以扩展为支持批量遗忘多个任务的逻辑
        # 例如：在任务5完成后同时遗忘任务2、4、5
        
        return tasks_to_forget
    
    def execute_forgetting(self, agent, task_id: int, test_loaders: List = None) -> Dict[str, Any]:
        """
        执行遗忘操作
        
        Args:
            agent: 持续学习代理
            task_id: 要遗忘的任务ID
            test_loaders: 测试数据加载器列表
            
        Returns:
            Dict[str, Any]: 遗忘结果
        """
        if self.verbose:
            print(f"\n======= 开始遗忘任务 {task_id} =======")
            self.print_gpu_memory_usage()

        # 清理GPU内存
        self.clear_gpu_memory()

        # 执行真正的模型参数遗忘
        results = self._perform_model_unlearning(agent, task_id, test_loaders)
        
        # 记录遗忘状态
        self.forgotten_tasks.add(task_id)
        self.forgetting_results[task_id] = results

        # 关键修复：从缓冲区中移除遗忘任务的数据
        self._remove_forgotten_task_from_buffer(agent, task_id)

        # 清理GPU内存
        self.clear_gpu_memory()

        if self.verbose:
            print(f"任务 {task_id} 遗忘完成")
            self.print_gpu_memory_usage()
            self._print_forgetting_results(results)

        return results
    
    def _perform_model_unlearning(self, agent, forget_task_id: int, 
                                 test_loaders: List = None) -> Dict[str, Any]:
        """
        执行真正的模型参数遗忘
        
        Args:
            agent: 持续学习代理
            forget_task_id: 要遗忘的任务ID
            test_loaders: 测试数据加载器列表
            
        Returns:
            Dict[str, Any]: 遗忘结果
        """
        # 获取缓冲区数据
        buffer_x, buffer_y, buffer_task_ids = agent.buffer.get_all_data()
        
        if buffer_x.size(0) == 0:
            print("警告：缓冲区为空，无法执行遗忘操作")
            return {"error": "Empty buffer"}
        
        # 分离遗忘数据和保留数据
        forget_mask = (buffer_task_ids == forget_task_id)
        retain_mask = ~forget_mask
        
        forget_x = buffer_x[forget_mask]
        forget_y = buffer_y[forget_mask]
        retain_x = buffer_x[retain_mask]
        retain_y = buffer_y[retain_mask]
        
        if forget_x.size(0) == 0:
            print(f"警告：缓冲区中没有任务 {forget_task_id} 的数据")
            return {"error": f"No data for task {forget_task_id}"}
        
        if self.verbose:
            print(f"遗忘数据: {forget_x.size(0)} 个样本")
            print(f"保留数据: {retain_x.size(0)} 个样本")
        
        # 评估遗忘前的准确率
        pre_forget_acc = self._evaluate_accuracy(agent, forget_x, forget_y)
        pre_retain_acc = self._evaluate_accuracy(agent, retain_x, retain_y) if retain_x.size(0) > 0 else 0.0
        
        # 如果有测试数据，也评估测试集准确率
        pre_forget_acc_test = 0.0
        pre_retain_acc_test = 0.0
        if test_loaders and len(test_loaders) > forget_task_id:
            pre_forget_acc_test = self._evaluate_test_accuracy(agent, test_loaders[forget_task_id])
            # 计算保留任务的平均测试准确率
            retain_accs = []
            for i, loader in enumerate(test_loaders):
                if i != forget_task_id and i < len(test_loaders):
                    acc = self._evaluate_test_accuracy(agent, loader)
                    retain_accs.append(acc)
            pre_retain_acc_test = np.mean(retain_accs) if retain_accs else 0.0
        
        # 执行遗忘训练
        self._unlearning_training(agent, forget_x, forget_y, retain_x, retain_y)

        # 关键修复：从缓冲区中移除遗忘任务的样本（如果启用）
        if self.remove_from_buffer:
            print(f"从缓冲区中移除任务 {forget_task_id} 的样本...")
            if hasattr(agent.buffer, 'remove_task_data'):
                agent.buffer.remove_task_data(forget_task_id)
            else:
                # 如果没有remove_task_data方法，手动移除
                self._remove_task_from_buffer(agent.buffer, forget_task_id)
        else:
            print(f"保留任务 {forget_task_id} 在缓冲区中的样本（remove_from_buffer=False）")

        # 评估遗忘后的准确率
        post_forget_acc = self._evaluate_accuracy(agent, forget_x, forget_y)
        post_retain_acc = self._evaluate_accuracy(agent, retain_x, retain_y) if retain_x.size(0) > 0 else 0.0
        
        # 测试集评估
        post_forget_acc_test = 0.0
        post_retain_acc_test = 0.0
        if test_loaders and len(test_loaders) > forget_task_id:
            post_forget_acc_test = self._evaluate_test_accuracy(agent, test_loaders[forget_task_id])
            # 计算保留任务的平均测试准确率
            retain_accs = []
            for i, loader in enumerate(test_loaders):
                if i != forget_task_id and i < len(test_loaders):
                    acc = self._evaluate_test_accuracy(agent, loader)
                    retain_accs.append(acc)
            post_retain_acc_test = np.mean(retain_accs) if retain_accs else 0.0
        
        # 计算遗忘效果指标
        results = {
            'task_id': forget_task_id,
            'forget_samples': forget_x.size(0),
            'retain_samples': retain_x.size(0),
            
            # 缓冲区评估结果
            'pre_forget_accuracy_buffer': pre_forget_acc,
            'post_forget_accuracy_buffer': post_forget_acc,
            'pre_retain_accuracy_buffer': pre_retain_acc,
            'post_retain_accuracy_buffer': post_retain_acc,
            'forget_accuracy_change_buffer': post_forget_acc - pre_forget_acc,
            'retain_accuracy_change_buffer': post_retain_acc - pre_retain_acc,
            
            # 测试集评估结果
            'pre_forget_accuracy_test': pre_forget_acc_test,
            'post_forget_accuracy_test': post_forget_acc_test,
            'pre_retain_accuracy_test': pre_retain_acc_test,
            'post_retain_accuracy_test': post_retain_acc_test,
            'forget_accuracy_change_test': post_forget_acc_test - pre_forget_acc_test,
            'retain_accuracy_change_test': post_retain_acc_test - pre_retain_acc_test,
        }
        
        # 评估遗忘成功性
        # 遗忘任务准确率应该接近随机猜测（对于3分类任务约为0.33）
        random_accuracy = 1.0 / 3.0  # 假设是3分类任务
        forget_success_threshold = 0.15  # 放宽偏差阈值，从0.1调整到0.15
        
        results['forget_success_buffer'] = abs(post_forget_acc - random_accuracy) < forget_success_threshold
        results['forget_success_test'] = abs(post_forget_acc_test - random_accuracy) < forget_success_threshold
        
        # 保留任务准确率下降应该很小（放宽到10%）
        retain_threshold = 0.10
        results['retain_success_buffer'] = abs(results['retain_accuracy_change_buffer']) < retain_threshold
        results['retain_success_test'] = abs(results['retain_accuracy_change_test']) < retain_threshold
        
        # 整体遗忘成功评估
        results['forget_success_overall'] = (results['forget_success_test'] and results['retain_success_test'])
        
        return results

    def _unlearning_training(self, agent, forget_x: torch.Tensor, forget_y: torch.Tensor,
                           retain_x: torch.Tensor, retain_y: torch.Tensor):
        """
        执行遗忘训练

        Args:
            agent: 持续学习代理
            forget_x: 要遗忘的数据
            forget_y: 要遗忘的标签
            retain_x: 要保留的数据
            retain_y: 要保留的标签
        """
        # 获取正确的设备
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            device = next(agent.model_encoder.parameters()).device
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            device = next(agent.model_ecoder.parameters()).device
        else:
            device = next(agent.model.parameters()).device

        # 将数据移到正确的设备
        forget_x = forget_x.to(device)
        forget_y = forget_y.to(device)
        retain_x = retain_x.to(device) if retain_x.size(0) > 0 else retain_x
        retain_y = retain_y.to(device) if retain_y.size(0) > 0 else retain_y

        # 设置模型为训练模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.train()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.train()
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            agent.model_ecoder.train()
            if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                agent.model_Cls.train()
        else:
            agent.model.train()

        # 创建优化器
        if hasattr(agent, 'opt'):
            optimizer = agent.opt
        else:
            # 如果没有优化器，创建一个
            if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                params = list(agent.model_encoder.parameters())
                if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                    params += list(agent.model_cls.parameters())
            else:
                params = agent.model.parameters()
            optimizer = torch.optim.SGD(params, lr=0.01)

        # 遗忘训练循环（分批处理以节省内存）
        forget_batch_size = 16  # 减小批处理大小
        retain_batch_size = 16

        for epoch in range(self.unlearning_epochs):
            total_loss = 0.0
            num_batches = 0

            # 计算需要的批次数
            forget_batches = (forget_x.size(0) + forget_batch_size - 1) // forget_batch_size if forget_x.size(0) > 0 else 0
            retain_batches = (retain_x.size(0) + retain_batch_size - 1) // retain_batch_size if retain_x.size(0) > 0 else 0
            max_batches = max(forget_batches, retain_batches)

            for batch_idx in range(max_batches):
                batch_loss = 0.0

                # 1. 遗忘损失：让模型对遗忘数据产生均匀分布的预测
                if forget_x.size(0) > 0 and batch_idx < forget_batches:
                    start_idx = batch_idx * forget_batch_size
                    end_idx = min(start_idx + forget_batch_size, forget_x.size(0))
                    batch_forget_x = forget_x[start_idx:end_idx]
                    batch_forget_y = forget_y[start_idx:end_idx]

                    # 前向传播
                    if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                        forget_features = agent.model_encoder(batch_forget_x)
                        if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                            forget_logits = agent.model_cls(forget_features)
                        else:
                            forget_logits = forget_features
                    elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                        forget_features = agent.model_ecoder(batch_forget_x)
                        if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                            forget_logits = agent.model_Cls(forget_features)
                        else:
                            forget_logits = forget_features
                    else:
                        forget_logits = agent.model(batch_forget_x)

                    # 计算遗忘损失：使用指定的遗忘方法，增加权重
                    forget_loss = self._compute_unlearning_loss(forget_logits, batch_forget_y, self.unlearning_method)
                    batch_loss += 2.0 * forget_loss  # 增加遗忘损失权重

                    # 清理中间变量
                    del batch_forget_x, batch_forget_y, forget_logits
                    if 'forget_features' in locals():
                        del forget_features

                # 2. 保留损失：最小化保留数据的损失
                if retain_x.size(0) > 0 and batch_idx < retain_batches:
                    start_idx = batch_idx * retain_batch_size
                    end_idx = min(start_idx + retain_batch_size, retain_x.size(0))
                    batch_retain_x = retain_x[start_idx:end_idx]
                    batch_retain_y = retain_y[start_idx:end_idx]

                    # 前向传播
                    if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                        retain_features = agent.model_encoder(batch_retain_x)
                        if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                            retain_logits = agent.model_cls(retain_features)
                        else:
                            retain_logits = retain_features
                    elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                        retain_features = agent.model_ecoder(batch_retain_x)
                        if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                            retain_logits = agent.model_Cls(retain_features)
                        else:
                            retain_logits = retain_features
                    else:
                        retain_logits = agent.model(batch_retain_x)

                    # 计算保留损失（正常的交叉熵）
                    retain_loss = F.cross_entropy(retain_logits, batch_retain_y)
                    batch_loss += 2.0 * retain_loss  # 给保留损失更高的权重

                    # 清理中间变量
                    del batch_retain_x, batch_retain_y, retain_logits
                    if 'retain_features' in locals():
                        del retain_features

                # 反向传播和优化
                if batch_loss != 0.0:
                    optimizer.zero_grad()
                    batch_loss.backward()
                    optimizer.step()
                    total_loss += batch_loss.item()
                    num_batches += 1

                # 清理GPU缓存
                torch.cuda.empty_cache()

            avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
            if self.verbose and epoch % 5 == 0:
                print(f"遗忘训练 Epoch {epoch}/{self.unlearning_epochs}, 平均Loss: {avg_loss:.4f}")

    def _compute_unlearning_loss(self, logits: torch.Tensor, true_labels: torch.Tensor,
                                method: str = "entropy_maximization") -> torch.Tensor:
        """
        计算遗忘损失

        Args:
            logits: 模型输出logits
            true_labels: 真实标签
            method: 遗忘方法

        Returns:
            torch.Tensor: 遗忘损失
        """
        num_classes = logits.size(1)

        if method == "entropy_maximization":
            # 方法1：最大化熵，让预测接近均匀分布
            probs = F.softmax(logits, dim=1)
            # 目标是均匀分布
            uniform_target = torch.ones_like(probs) / num_classes
            # 使用KL散度让预测分布接近均匀分布
            kl_loss = F.kl_div(F.log_softmax(logits, dim=1), uniform_target, reduction='batchmean')
            return kl_loss

        elif method == "confidence_reduction":
            # 方法2：降低预测置信度
            probs = F.softmax(logits, dim=1)
            max_probs = torch.max(probs, dim=1)[0]
            # 惩罚高置信度预测
            confidence_loss = torch.mean(max_probs)
            return confidence_loss

        elif method == "label_smoothing":
            # 方法3：标签平滑 + 反向学习
            smoothed_targets = torch.ones_like(logits) / num_classes
            # 对真实标签位置给予较小的权重
            smoothed_targets.scatter_(1, true_labels.unsqueeze(1), 0.1)
            # 重新归一化
            smoothed_targets = smoothed_targets / smoothed_targets.sum(dim=1, keepdim=True)

            log_probs = F.log_softmax(logits, dim=1)
            smooth_loss = -torch.sum(smoothed_targets * log_probs, dim=1).mean()
            return smooth_loss

        elif method == "adversarial":
            # 方法4：对抗性遗忘（稳定版本）
            # 使用梯度上升的思想，但限制幅度避免不稳定
            ce_loss = F.cross_entropy(logits, true_labels, reduction='none')
            # 使用sigmoid来限制损失的范围，避免梯度爆炸
            adversarial_loss = -torch.sigmoid(ce_loss).mean()
            return adversarial_loss

        elif method == "hybrid":
            # 方法5：混合方法
            # 结合熵最大化和置信度降低
            probs = F.softmax(logits, dim=1)

            # 熵最大化部分
            uniform_target = torch.ones_like(probs) / num_classes
            entropy_loss = F.kl_div(F.log_softmax(logits, dim=1), uniform_target, reduction='batchmean')

            # 置信度降低部分
            max_probs = torch.max(probs, dim=1)[0]
            confidence_loss = torch.mean(max_probs)

            # 组合损失 - 增加熵最大化的权重，使遗忘更彻底
            hybrid_loss = 0.8 * entropy_loss + 0.2 * confidence_loss
            return hybrid_loss

        elif method == "aggressive":
            # 方法6：激进遗忘方法
            # 结合多种技术实现更彻底的遗忘
            probs = F.softmax(logits, dim=1)

            # 1. 熵最大化
            uniform_target = torch.ones_like(probs) / num_classes
            entropy_loss = F.kl_div(F.log_softmax(logits, dim=1), uniform_target, reduction='batchmean')

            # 2. 反向标签学习（让模型学习错误的标签）
            # 创建反向标签：将真实标签映射到其他类别
            reverse_labels = (true_labels + 1) % num_classes
            reverse_loss = F.cross_entropy(logits, reverse_labels)

            # 3. 置信度惩罚
            max_probs = torch.max(probs, dim=1)[0]
            confidence_penalty = torch.mean(max_probs)

            # 组合损失
            aggressive_loss = 0.5 * entropy_loss + 0.3 * reverse_loss + 0.2 * confidence_penalty
            return aggressive_loss

        else:
            # 默认使用熵最大化
            return self._compute_unlearning_loss(logits, true_labels, "entropy_maximization")

    @memory_optimized(clear_cache=True)
    def _evaluate_accuracy(self, agent, x: torch.Tensor, y: torch.Tensor, batch_size: int = None) -> float:
        """
        评估模型在给定数据上的准确率（分批处理以节省内存）

        Args:
            agent: 持续学习代理
            x: 输入数据
            y: 标签
            batch_size: 批处理大小，如果为None则使用内存配置

        Returns:
            float: 准确率
        """
        if x.size(0) == 0:
            return 0.0

        # 使用内存配置的批处理大小
        if batch_size is None:
            batch_size = self.memory_config.adaptive_batch_size(
                self.memory_config.eval_batch_size, x.size(0)
            )

        # 获取正确的设备
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            device = next(agent.model_encoder.parameters()).device
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            device = next(agent.model_ecoder.parameters()).device
        else:
            device = next(agent.model.parameters()).device
        x = x.to(device)
        y = y.to(device)

        # 设置模型为评估模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.eval()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.eval()
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            agent.model_ecoder.eval()
            if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                agent.model_Cls.eval()
        else:
            agent.model.eval()

        total_correct = 0
        total_samples = 0

        with torch.no_grad():
            # 分批处理以节省内存
            for i in range(0, x.size(0), batch_size):
                batch_x = x[i:i+batch_size]
                batch_y = y[i:i+batch_size]

                # 前向传播
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                    features = agent.model_ecoder(batch_x)
                    if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                        logits = agent.model_Cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                # 计算预测
                predictions = torch.argmax(logits, dim=1)
                total_correct += (predictions == batch_y).sum().item()
                total_samples += batch_y.size(0)

                # 清理中间变量以释放内存
                del batch_x, batch_y, logits, predictions
                if 'features' in locals():
                    del features
                torch.cuda.empty_cache()  # 清理GPU缓存

        accuracy = total_correct / total_samples if total_samples > 0 else 0.0
        return accuracy

    def _evaluate_test_accuracy(self, agent, test_loader) -> float:
        """
        评估模型在测试集上的准确率

        Args:
            agent: 持续学习代理
            test_loader: 测试数据加载器

        Returns:
            float: 准确率
        """
        if test_loader is None:
            return 0.0

        # 设置模型为评估模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.eval()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.eval()
        else:
            agent.model.eval()

        total_correct = 0
        total_samples = 0

        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                # 获取正确的设备
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    device = next(agent.model_encoder.parameters()).device
                elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                    device = next(agent.model_ecoder.parameters()).device
                else:
                    device = next(agent.model.parameters()).device
                batch_x = batch_x.to(device)
                batch_y = batch_y.to(device)

                # 前向传播
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                # 计算预测
                predictions = torch.argmax(logits, dim=1)
                total_correct += (predictions == batch_y).sum().item()
                total_samples += batch_y.size(0)

        return total_correct / total_samples if total_samples > 0 else 0.0

    def _print_forgetting_results(self, results: Dict[str, Any]):
        """
        打印遗忘结果

        Args:
            results: 遗忘结果字典
        """
        print("\n====== 遗忘效果评估 ======")
        print(f"遗忘任务ID: {results['task_id']}")
        print(f"遗忘样本数: {results['forget_samples']}")
        print(f"保留样本数: {results['retain_samples']}")

        # 优先显示测试集结果
        if 'pre_forget_accuracy_test' in results and results['pre_forget_accuracy_test'] > 0:
            print("\n----- 测试集评估结果 -----")
            print(f"遗忘前准确率 - 遗忘任务: {results['pre_forget_accuracy_test']:.4f}, 保留任务: {results['pre_retain_accuracy_test']:.4f}")
            print(f"遗忘后准确率 - 遗忘任务: {results['post_forget_accuracy_test']:.4f}, 保留任务: {results['post_retain_accuracy_test']:.4f}")
            print(f"准确率变化 - 遗忘任务: {results['forget_accuracy_change_test']:.4f}, 保留任务: {results['retain_accuracy_change_test']:.4f}")

            # 遗忘成功评估
            if results['forget_success_test']:
                print("✓ 遗忘成功: 遗忘任务准确率接近随机猜测")
            else:
                print("✗ 遗忘不完全: 遗忘任务准确率偏离随机猜测")

            if results['retain_success_test']:
                print("✓ 保留成功: 保留任务准确率下降很小")
            else:
                print("✗ 保留失败: 保留任务准确率下降过多")

        print("\n----- 缓冲区评估结果 -----")
        print(f"遗忘前准确率 - 遗忘任务: {results['pre_forget_accuracy_buffer']:.4f}, 保留任务: {results['pre_retain_accuracy_buffer']:.4f}")
        print(f"遗忘后准确率 - 遗忘任务: {results['post_forget_accuracy_buffer']:.4f}, 保留任务: {results['post_retain_accuracy_buffer']:.4f}")
        print(f"准确率变化 - 遗忘任务: {results['forget_accuracy_change_buffer']:.4f}, 保留任务: {results['retain_accuracy_change_buffer']:.4f}")

        print(f"\n整体遗忘成功: {results['forget_success_overall']}")

    def get_forgetting_summary(self) -> Dict[str, Any]:
        """
        获取遗忘操作的总结

        Returns:
            Dict[str, Any]: 遗忘总结
        """
        summary = {
            'total_tasks': self.total_tasks,
            'forget_task_ids': sorted(list(self.forget_task_ids)),
            'forgotten_tasks': sorted(list(self.forgotten_tasks)),
            'remaining_forget_tasks': sorted(list(self.forget_task_ids - self.forgotten_tasks)),
            'forgetting_results': self.forgetting_results
        }

        return summary

    def _remove_forgotten_task_from_buffer(self, agent, task_id: int):
        """
        从缓冲区中移除遗忘任务的数据

        Args:
            agent: 持续学习代理
            task_id: 要移除的任务ID
        """
        if self.verbose:
            print(f"从缓冲区中移除任务 {task_id} 的数据...")

        # 获取缓冲区数据
        buffer_x, _, buffer_task_ids = agent.buffer.get_all_data()

        if buffer_x.size(0) == 0:
            print("缓冲区为空，无需清理")
            return

        # 统计移除前的样本数
        forget_mask = (buffer_task_ids == task_id)
        samples_to_remove = forget_mask.sum().item()

        if samples_to_remove == 0:
            print(f"缓冲区中没有任务 {task_id} 的数据")
            return

        print(f"准备移除 {samples_to_remove} 个任务 {task_id} 的样本")

        # 使用缓冲区的remove_task_data方法
        if hasattr(agent.buffer, 'remove_task_data'):
            agent.buffer.remove_task_data(task_id)
        else:
            # 如果没有该方法，手动实现
            self._manual_remove_task_data(agent.buffer, task_id)

        # 验证移除结果
        _, _, buffer_task_ids_after = agent.buffer.get_all_data()
        remaining_samples = (buffer_task_ids_after == task_id).sum().item() if buffer_task_ids_after.size(0) > 0 else 0

        if remaining_samples == 0:
            print(f"✓ 成功移除任务 {task_id} 的所有数据")
        else:
            print(f"⚠️ 仍有 {remaining_samples} 个任务 {task_id} 的样本残留")

        # 打印移除后的缓冲区分布
        if hasattr(agent.buffer, 'analyze_buffer_distribution'):
            agent.buffer.analyze_buffer_distribution()

    def _manual_remove_task_data(self, buffer, task_id: int):
        """
        手动移除缓冲区中指定任务的数据

        Args:
            buffer: 缓冲区对象
            task_id: 要移除的任务ID
        """
        if buffer.current_index == 0:
            return

        # 找到不是指定任务的数据
        keep_mask = buffer.tasks[:buffer.current_index] != task_id
        keep_indices = torch.where(keep_mask)[0]

        if len(keep_indices) == 0:
            # 所有数据都被移除
            buffer.current_index = 0
            return

        # 重新排列数据
        buffer.buffer_img[:len(keep_indices)] = buffer.buffer_img[keep_indices]
        buffer.buffer_label[:len(keep_indices)] = buffer.buffer_label[keep_indices]
        buffer.tasks[:len(keep_indices)] = buffer.tasks[keep_indices]

        # 更新索引
        buffer.current_index = len(keep_indices)

        print(f"手动移除完成，剩余 {buffer.current_index} 个样本")

    @staticmethod
    def clear_gpu_memory():
        """清理GPU内存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()

    @staticmethod
    def print_gpu_memory_usage():
        """打印GPU内存使用情况"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            reserved = torch.cuda.memory_reserved() / 1024**3   # GB
            print(f"GPU内存使用: 已分配 {allocated:.2f}GB, 已保留 {reserved:.2f}GB")

    def _safe_model_forward(self, agent, x: torch.Tensor, batch_size: int = 16):
        """
        安全的模型前向传播，分批处理以避免内存溢出

        Args:
            agent: 持续学习代理
            x: 输入数据
            batch_size: 批处理大小

        Returns:
            torch.Tensor: 模型输出
        """
        if x.size(0) == 0:
            return torch.empty(0, device=x.device)

        outputs = []
        with torch.no_grad():
            for i in range(0, x.size(0), batch_size):
                batch_x = x[i:i+batch_size]

                # 前向传播
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        batch_output = agent.model_cls(features)
                    else:
                        batch_output = features
                elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                    features = agent.model_ecoder(batch_x)
                    if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                        batch_output = agent.model_Cls(features)
                    else:
                        batch_output = features
                else:
                    batch_output = agent.model(batch_x)

                outputs.append(batch_output.cpu())  # 移到CPU以节省GPU内存

                # 清理中间变量
                del batch_x, batch_output
                if 'features' in locals():
                    del features
                self.clear_gpu_memory()

        # 合并输出并移回GPU
        result = torch.cat(outputs, dim=0)
        device = next(agent.model_encoder.parameters()).device if hasattr(agent, 'model_encoder') and agent.model_encoder is not None else next(agent.model.parameters()).device
        return result.to(device)

    def _remove_task_from_buffer(self, buffer, task_id: int):
        """
        从缓冲区中移除指定任务的样本

        Args:
            buffer: 缓冲区对象
            task_id: 要移除的任务ID
        """
        if not hasattr(buffer, 'tasks') or buffer.current_index == 0:
            print(f"缓冲区为空或没有任务ID信息，无法移除任务 {task_id}")
            return

        # 找到不是指定任务的样本
        valid_indices = []
        for i in range(buffer.current_index):
            if buffer.tasks[i].item() != task_id:
                valid_indices.append(i)

        if len(valid_indices) == 0:
            print(f"移除任务 {task_id} 后缓冲区为空")
            buffer.current_index = 0
            return

        print(f"移除任务 {task_id}: 从 {buffer.current_index} 个样本减少到 {len(valid_indices)} 个样本")

        # 重新排列缓冲区数据
        valid_indices = torch.tensor(valid_indices)

        # 移动有效数据到缓冲区前部
        buffer.buffer_img[:len(valid_indices)] = buffer.buffer_img[valid_indices]
        buffer.buffer_label[:len(valid_indices)] = buffer.buffer_label[valid_indices]
        buffer.tasks[:len(valid_indices)] = buffer.tasks[valid_indices]

        # 更新当前索引
        buffer.current_index = len(valid_indices)

        print(f"缓冲区更新完成，当前包含 {buffer.current_index} 个样本")

        # 分析更新后的缓冲区分布
        if hasattr(buffer, 'analyze_buffer_distribution'):
            buffer.analyze_buffer_distribution()

    def evaluate_membership_inference_attack(self, agent, forget_x: torch.Tensor, forget_y: torch.Tensor,
                                           retain_x: torch.Tensor, retain_y: torch.Tensor) -> Tuple[float, float]:
        """
        评估成员推理攻击，用于验证遗忘效果

        Args:
            agent: 持续学习代理
            forget_x: 遗忘数据
            forget_y: 遗忘标签
            retain_x: 保留数据
            retain_y: 保留标签

        Returns:
            Tuple[float, float]: (MIA准确率, MIA AUC)
        """
        if forget_x.size(0) == 0 or retain_x.size(0) == 0:
            return 0.5, 0.5  # 随机猜测基线

        # 获取正确的设备
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            device = next(agent.model_encoder.parameters()).device
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            device = next(agent.model_ecoder.parameters()).device
        else:
            device = next(agent.model.parameters()).device

        # 设置模型为评估模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.eval()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.eval()
        else:
            agent.model.eval()

        # 计算损失作为成员推理的特征（使用更小的批处理大小）
        forget_losses = []
        retain_losses = []
        batch_size = 16  # 减小批处理大小以节省内存

        with torch.no_grad():
            # 计算遗忘数据的损失
            for i in range(0, forget_x.size(0), batch_size):
                batch_x = forget_x[i:i+batch_size].to(device)
                batch_y = forget_y[i:i+batch_size].to(device)

                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                    features = agent.model_ecoder(batch_x)
                    if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                        logits = agent.model_Cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                losses = F.cross_entropy(logits, batch_y, reduction='none')
                forget_losses.extend(losses.cpu().numpy())

                # 清理中间变量
                del batch_x, batch_y, logits, losses
                if 'features' in locals():
                    del features
                torch.cuda.empty_cache()

            # 计算保留数据的损失
            for i in range(0, retain_x.size(0), batch_size):
                batch_x = retain_x[i:i+batch_size].to(device)
                batch_y = retain_y[i:i+batch_size].to(device)

                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                    features = agent.model_ecoder(batch_x)
                    if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                        logits = agent.model_Cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                losses = F.cross_entropy(logits, batch_y, reduction='none')
                retain_losses.extend(losses.cpu().numpy())

                # 清理中间变量
                del batch_x, batch_y, logits, losses
                if 'features' in locals():
                    del features
                torch.cuda.empty_cache()

        # 构建MIA数据集
        # 标签：1表示成员（保留数据），0表示非成员（遗忘数据）
        mia_features = np.array(forget_losses + retain_losses)
        mia_labels = np.array([0] * len(forget_losses) + [1] * len(retain_losses))

        # 简单的阈值分类器：损失越低，越可能是成员
        threshold = np.median(mia_features)
        mia_predictions = (mia_features < threshold).astype(int)

        # 计算准确率
        mia_accuracy = np.mean(mia_predictions == mia_labels)

        # 计算AUC
        try:
            mia_auc = roc_auc_score(mia_labels, -mia_features)  # 负号因为损失越低越可能是成员
        except:
            mia_auc = 0.5

        return mia_accuracy, mia_auc

    def validate_forgetting_quality(self, agent, forget_task_id: int, test_loaders: List = None) -> Dict[str, Any]:
        """
        验证遗忘质量

        Args:
            agent: 持续学习代理
            forget_task_id: 遗忘的任务ID
            test_loaders: 测试数据加载器列表

        Returns:
            Dict[str, Any]: 验证结果
        """
        results = {}

        # 1. 检查遗忘任务的准确率是否接近随机猜测
        if test_loaders and len(test_loaders) > forget_task_id:
            forget_acc = self._evaluate_test_accuracy(agent, test_loaders[forget_task_id])
            random_acc = 1.0 / 3.0  # 假设3分类

            results['forget_task_accuracy'] = forget_acc
            results['random_accuracy'] = random_acc
            results['accuracy_diff_from_random'] = abs(forget_acc - random_acc)
            results['is_close_to_random'] = results['accuracy_diff_from_random'] < 0.1

            # 2. 检查保留任务的准确率是否保持
            retain_accs = []
            for i, loader in enumerate(test_loaders):
                if i != forget_task_id:
                    acc = self._evaluate_test_accuracy(agent, loader)
                    retain_accs.append(acc)

            if retain_accs:
                results['retain_tasks_accuracy'] = np.mean(retain_accs)
                results['retain_tasks_std'] = np.std(retain_accs)

        # 3. 成员推理攻击评估
        buffer_x, buffer_y, buffer_task_ids = agent.buffer.get_all_data()
        if buffer_x.size(0) > 0:
            forget_mask = (buffer_task_ids == forget_task_id)
            retain_mask = ~forget_mask

            forget_x = buffer_x[forget_mask]
            forget_y = buffer_y[forget_mask]
            retain_x = buffer_x[retain_mask]
            retain_y = buffer_y[retain_mask]

            if forget_x.size(0) > 0 and retain_x.size(0) > 0:
                mia_acc, mia_auc = self.evaluate_membership_inference_attack(
                    agent, forget_x, forget_y, retain_x, retain_y
                )
                results['mia_accuracy'] = mia_acc
                results['mia_auc'] = mia_auc
                results['mia_close_to_random'] = abs(mia_acc - 0.5) < 0.1

        # 4. 整体遗忘质量评估
        quality_checks = []
        if 'is_close_to_random' in results:
            quality_checks.append(results['is_close_to_random'])
        if 'mia_close_to_random' in results:
            quality_checks.append(results['mia_close_to_random'])

        results['overall_forgetting_quality'] = all(quality_checks) if quality_checks else False

        return results
