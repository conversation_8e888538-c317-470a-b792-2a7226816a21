"""
遗忘调度器模块
实现正确的遗忘学习模式：在指定任务训练完成后立即执行遗忘
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import copy
from sklearn.metrics import roc_auc_score


class ForgettingScheduler:
    """
    遗忘调度器类
    
    负责管理遗忘任务列表、遗忘时机和遗忘执行
    实现论文中描述的正确遗忘模式：
    1. 指定要遗忘的任务列表
    2. 在指定任务训练完成后立即执行遗忘
    3. 遗忘后继续训练后续任务
    """
    
    def __init__(self, forget_task_ids: List[int], total_tasks: int, 
                 unlearning_epochs: int = 20, verbose: bool = True):
        """
        初始化遗忘调度器
        
        Args:
            forget_task_ids: 要遗忘的任务ID列表
            total_tasks: 总任务数
            unlearning_epochs: 遗忘训练轮数
            verbose: 是否打印详细信息
        """
        self.forget_task_ids = set(forget_task_ids) if forget_task_ids else set()
        self.total_tasks = total_tasks
        self.unlearning_epochs = unlearning_epochs
        self.verbose = verbose
        
        # 遗忘状态跟踪
        self.forgotten_tasks = set()  # 已遗忘的任务
        self.current_task = -1  # 当前训练的任务ID
        
        # 遗忘效果记录
        self.forgetting_results = {}  # 存储每次遗忘的结果
        
        if self.verbose:
            print(f"遗忘调度器初始化完成")
            print(f"要遗忘的任务: {sorted(list(self.forget_task_ids))}")
            print(f"总任务数: {total_tasks}")
    
    def should_forget_after_task(self, task_id: int) -> bool:
        """
        检查在指定任务训练完成后是否需要执行遗忘
        
        Args:
            task_id: 刚完成训练的任务ID
            
        Returns:
            bool: 是否需要执行遗忘
        """
        self.current_task = task_id
        
        # 检查是否需要遗忘刚训练完的任务
        if task_id in self.forget_task_ids and task_id not in self.forgotten_tasks:
            return True
        
        return False
    
    def get_tasks_to_forget(self, completed_task_id: int) -> List[int]:
        """
        获取需要遗忘的任务列表
        
        Args:
            completed_task_id: 刚完成训练的任务ID
            
        Returns:
            List[int]: 需要遗忘的任务ID列表
        """
        tasks_to_forget = []
        
        # 检查是否需要遗忘刚完成的任务
        if completed_task_id in self.forget_task_ids and completed_task_id not in self.forgotten_tasks:
            tasks_to_forget.append(completed_task_id)
        
        # 可以扩展为支持批量遗忘多个任务的逻辑
        # 例如：在任务5完成后同时遗忘任务2、4、5
        
        return tasks_to_forget
    
    def execute_forgetting(self, agent, task_id: int, test_loaders: List = None) -> Dict[str, Any]:
        """
        执行遗忘操作
        
        Args:
            agent: 持续学习代理
            task_id: 要遗忘的任务ID
            test_loaders: 测试数据加载器列表
            
        Returns:
            Dict[str, Any]: 遗忘结果
        """
        if self.verbose:
            print(f"\n======= 开始遗忘任务 {task_id} =======")
        
        # 执行真正的模型参数遗忘
        results = self._perform_model_unlearning(agent, task_id, test_loaders)
        
        # 记录遗忘状态
        self.forgotten_tasks.add(task_id)
        self.forgetting_results[task_id] = results
        
        if self.verbose:
            print(f"任务 {task_id} 遗忘完成")
            self._print_forgetting_results(results)
        
        return results
    
    def _perform_model_unlearning(self, agent, forget_task_id: int, 
                                 test_loaders: List = None) -> Dict[str, Any]:
        """
        执行真正的模型参数遗忘
        
        Args:
            agent: 持续学习代理
            forget_task_id: 要遗忘的任务ID
            test_loaders: 测试数据加载器列表
            
        Returns:
            Dict[str, Any]: 遗忘结果
        """
        # 获取缓冲区数据
        buffer_x, buffer_y, buffer_task_ids = agent.buffer.get_all_data()
        
        if buffer_x.size(0) == 0:
            print("警告：缓冲区为空，无法执行遗忘操作")
            return {"error": "Empty buffer"}
        
        # 分离遗忘数据和保留数据
        forget_mask = (buffer_task_ids == forget_task_id)
        retain_mask = ~forget_mask
        
        forget_x = buffer_x[forget_mask]
        forget_y = buffer_y[forget_mask]
        retain_x = buffer_x[retain_mask]
        retain_y = buffer_y[retain_mask]
        
        if forget_x.size(0) == 0:
            print(f"警告：缓冲区中没有任务 {forget_task_id} 的数据")
            return {"error": f"No data for task {forget_task_id}"}
        
        if self.verbose:
            print(f"遗忘数据: {forget_x.size(0)} 个样本")
            print(f"保留数据: {retain_x.size(0)} 个样本")
        
        # 评估遗忘前的准确率
        pre_forget_acc = self._evaluate_accuracy(agent, forget_x, forget_y)
        pre_retain_acc = self._evaluate_accuracy(agent, retain_x, retain_y) if retain_x.size(0) > 0 else 0.0
        
        # 如果有测试数据，也评估测试集准确率
        pre_forget_acc_test = 0.0
        pre_retain_acc_test = 0.0
        if test_loaders and len(test_loaders) > forget_task_id:
            pre_forget_acc_test = self._evaluate_test_accuracy(agent, test_loaders[forget_task_id])
            # 计算保留任务的平均测试准确率
            retain_accs = []
            for i, loader in enumerate(test_loaders):
                if i != forget_task_id and i < len(test_loaders):
                    acc = self._evaluate_test_accuracy(agent, loader)
                    retain_accs.append(acc)
            pre_retain_acc_test = np.mean(retain_accs) if retain_accs else 0.0
        
        # 执行遗忘训练
        self._unlearning_training(agent, forget_x, forget_y, retain_x, retain_y)
        
        # 评估遗忘后的准确率
        post_forget_acc = self._evaluate_accuracy(agent, forget_x, forget_y)
        post_retain_acc = self._evaluate_accuracy(agent, retain_x, retain_y) if retain_x.size(0) > 0 else 0.0
        
        # 测试集评估
        post_forget_acc_test = 0.0
        post_retain_acc_test = 0.0
        if test_loaders and len(test_loaders) > forget_task_id:
            post_forget_acc_test = self._evaluate_test_accuracy(agent, test_loaders[forget_task_id])
            # 计算保留任务的平均测试准确率
            retain_accs = []
            for i, loader in enumerate(test_loaders):
                if i != forget_task_id and i < len(test_loaders):
                    acc = self._evaluate_test_accuracy(agent, loader)
                    retain_accs.append(acc)
            post_retain_acc_test = np.mean(retain_accs) if retain_accs else 0.0
        
        # 计算遗忘效果指标
        results = {
            'task_id': forget_task_id,
            'forget_samples': forget_x.size(0),
            'retain_samples': retain_x.size(0),
            
            # 缓冲区评估结果
            'pre_forget_accuracy_buffer': pre_forget_acc,
            'post_forget_accuracy_buffer': post_forget_acc,
            'pre_retain_accuracy_buffer': pre_retain_acc,
            'post_retain_accuracy_buffer': post_retain_acc,
            'forget_accuracy_change_buffer': post_forget_acc - pre_forget_acc,
            'retain_accuracy_change_buffer': post_retain_acc - pre_retain_acc,
            
            # 测试集评估结果
            'pre_forget_accuracy_test': pre_forget_acc_test,
            'post_forget_accuracy_test': post_forget_acc_test,
            'pre_retain_accuracy_test': pre_retain_acc_test,
            'post_retain_accuracy_test': post_retain_acc_test,
            'forget_accuracy_change_test': post_forget_acc_test - pre_forget_acc_test,
            'retain_accuracy_change_test': post_retain_acc_test - pre_retain_acc_test,
        }
        
        # 评估遗忘成功性
        # 遗忘任务准确率应该接近随机猜测（对于3分类任务约为0.33）
        random_accuracy = 1.0 / 3.0  # 假设是3分类任务
        forget_success_threshold = 0.1  # 允许的偏差
        
        results['forget_success_buffer'] = abs(post_forget_acc - random_accuracy) < forget_success_threshold
        results['forget_success_test'] = abs(post_forget_acc_test - random_accuracy) < forget_success_threshold
        
        # 保留任务准确率下降应该很小（小于5%）
        retain_threshold = 0.05
        results['retain_success_buffer'] = abs(results['retain_accuracy_change_buffer']) < retain_threshold
        results['retain_success_test'] = abs(results['retain_accuracy_change_test']) < retain_threshold
        
        # 整体遗忘成功评估
        results['forget_success_overall'] = (results['forget_success_test'] and results['retain_success_test'])
        
        return results

    def _unlearning_training(self, agent, forget_x: torch.Tensor, forget_y: torch.Tensor,
                           retain_x: torch.Tensor, retain_y: torch.Tensor):
        """
        执行遗忘训练

        Args:
            agent: 持续学习代理
            forget_x: 要遗忘的数据
            forget_y: 要遗忘的标签
            retain_x: 要保留的数据
            retain_y: 要保留的标签
        """
        # 获取正确的设备
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            device = next(agent.model_encoder.parameters()).device
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            device = next(agent.model_ecoder.parameters()).device
        else:
            device = next(agent.model.parameters()).device

        # 将数据移到正确的设备
        forget_x = forget_x.to(device)
        forget_y = forget_y.to(device)
        retain_x = retain_x.to(device) if retain_x.size(0) > 0 else retain_x
        retain_y = retain_y.to(device) if retain_y.size(0) > 0 else retain_y

        # 设置模型为训练模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.train()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.train()
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            agent.model_ecoder.train()
            if hasattr(agent, 'model_Cls') and agent.model_Cls is not None:
                agent.model_Cls.train()
        else:
            agent.model.train()

        # 创建优化器
        if hasattr(agent, 'opt'):
            optimizer = agent.opt
        else:
            # 如果没有优化器，创建一个
            if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                params = list(agent.model_encoder.parameters())
                if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                    params += list(agent.model_cls.parameters())
            else:
                params = agent.model.parameters()
            optimizer = torch.optim.SGD(params, lr=0.01)

        # 遗忘训练循环
        for epoch in range(self.unlearning_epochs):
            total_loss = 0.0

            # 1. 遗忘损失：最大化遗忘数据的损失（让模型"忘记"这些数据）
            if forget_x.size(0) > 0:
                # 前向传播
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    forget_features = agent.model_encoder(forget_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        forget_logits = agent.model_cls(forget_features)
                    else:
                        forget_logits = forget_features
                else:
                    forget_logits = agent.model(forget_x)

                # 计算遗忘损失（负的交叉熵，即最大化损失）
                forget_loss = -F.cross_entropy(forget_logits, forget_y)
                total_loss += forget_loss

            # 2. 保留损失：最小化保留数据的损失（保持对这些数据的记忆）
            if retain_x.size(0) > 0:
                # 前向传播
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    retain_features = agent.model_encoder(retain_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        retain_logits = agent.model_cls(retain_features)
                    else:
                        retain_logits = retain_features
                else:
                    retain_logits = agent.model(retain_x)

                # 计算保留损失（正常的交叉熵）
                retain_loss = F.cross_entropy(retain_logits, retain_y)
                total_loss += 2.0 * retain_loss  # 给保留损失更高的权重

            # 反向传播和优化
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()

            if self.verbose and epoch % 5 == 0:
                print(f"遗忘训练 Epoch {epoch}/{self.unlearning_epochs}, Loss: {total_loss.item():.4f}")

    def _evaluate_accuracy(self, agent, x: torch.Tensor, y: torch.Tensor) -> float:
        """
        评估模型在给定数据上的准确率

        Args:
            agent: 持续学习代理
            x: 输入数据
            y: 标签

        Returns:
            float: 准确率
        """
        if x.size(0) == 0:
            return 0.0

        # 获取正确的设备
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            device = next(agent.model_encoder.parameters()).device
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            device = next(agent.model_ecoder.parameters()).device
        else:
            device = next(agent.model.parameters()).device
        x = x.to(device)
        y = y.to(device)

        # 设置模型为评估模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.eval()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.eval()
        else:
            agent.model.eval()

        with torch.no_grad():
            # 前向传播
            if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                features = agent.model_encoder(x)
                if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                    logits = agent.model_cls(features)
                else:
                    logits = features
            else:
                logits = agent.model(x)

            # 计算预测
            predictions = torch.argmax(logits, dim=1)
            correct = (predictions == y).float()
            accuracy = correct.mean().item()

        return accuracy

    def _evaluate_test_accuracy(self, agent, test_loader) -> float:
        """
        评估模型在测试集上的准确率

        Args:
            agent: 持续学习代理
            test_loader: 测试数据加载器

        Returns:
            float: 准确率
        """
        if test_loader is None:
            return 0.0

        # 设置模型为评估模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.eval()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.eval()
        else:
            agent.model.eval()

        total_correct = 0
        total_samples = 0

        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                # 获取正确的设备
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    device = next(agent.model_encoder.parameters()).device
                elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
                    device = next(agent.model_ecoder.parameters()).device
                else:
                    device = next(agent.model.parameters()).device
                batch_x = batch_x.to(device)
                batch_y = batch_y.to(device)

                # 前向传播
                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                # 计算预测
                predictions = torch.argmax(logits, dim=1)
                total_correct += (predictions == batch_y).sum().item()
                total_samples += batch_y.size(0)

        return total_correct / total_samples if total_samples > 0 else 0.0

    def _print_forgetting_results(self, results: Dict[str, Any]):
        """
        打印遗忘结果

        Args:
            results: 遗忘结果字典
        """
        print("\n====== 遗忘效果评估 ======")
        print(f"遗忘任务ID: {results['task_id']}")
        print(f"遗忘样本数: {results['forget_samples']}")
        print(f"保留样本数: {results['retain_samples']}")

        # 优先显示测试集结果
        if 'pre_forget_accuracy_test' in results and results['pre_forget_accuracy_test'] > 0:
            print("\n----- 测试集评估结果 -----")
            print(f"遗忘前准确率 - 遗忘任务: {results['pre_forget_accuracy_test']:.4f}, 保留任务: {results['pre_retain_accuracy_test']:.4f}")
            print(f"遗忘后准确率 - 遗忘任务: {results['post_forget_accuracy_test']:.4f}, 保留任务: {results['post_retain_accuracy_test']:.4f}")
            print(f"准确率变化 - 遗忘任务: {results['forget_accuracy_change_test']:.4f}, 保留任务: {results['retain_accuracy_change_test']:.4f}")

            # 遗忘成功评估
            if results['forget_success_test']:
                print("✓ 遗忘成功: 遗忘任务准确率接近随机猜测")
            else:
                print("✗ 遗忘不完全: 遗忘任务准确率偏离随机猜测")

            if results['retain_success_test']:
                print("✓ 保留成功: 保留任务准确率下降很小")
            else:
                print("✗ 保留失败: 保留任务准确率下降过多")

        print("\n----- 缓冲区评估结果 -----")
        print(f"遗忘前准确率 - 遗忘任务: {results['pre_forget_accuracy_buffer']:.4f}, 保留任务: {results['pre_retain_accuracy_buffer']:.4f}")
        print(f"遗忘后准确率 - 遗忘任务: {results['post_forget_accuracy_buffer']:.4f}, 保留任务: {results['post_retain_accuracy_buffer']:.4f}")
        print(f"准确率变化 - 遗忘任务: {results['forget_accuracy_change_buffer']:.4f}, 保留任务: {results['retain_accuracy_change_buffer']:.4f}")

        print(f"\n整体遗忘成功: {results['forget_success_overall']}")

    def get_forgetting_summary(self) -> Dict[str, Any]:
        """
        获取遗忘操作的总结

        Returns:
            Dict[str, Any]: 遗忘总结
        """
        summary = {
            'total_tasks': self.total_tasks,
            'forget_task_ids': sorted(list(self.forget_task_ids)),
            'forgotten_tasks': sorted(list(self.forgotten_tasks)),
            'remaining_forget_tasks': sorted(list(self.forget_task_ids - self.forgotten_tasks)),
            'forgetting_results': self.forgetting_results
        }

        return summary

    def evaluate_membership_inference_attack(self, agent, forget_x: torch.Tensor, forget_y: torch.Tensor,
                                           retain_x: torch.Tensor, retain_y: torch.Tensor) -> Tuple[float, float]:
        """
        评估成员推理攻击，用于验证遗忘效果

        Args:
            agent: 持续学习代理
            forget_x: 遗忘数据
            forget_y: 遗忘标签
            retain_x: 保留数据
            retain_y: 保留标签

        Returns:
            Tuple[float, float]: (MIA准确率, MIA AUC)
        """
        if forget_x.size(0) == 0 or retain_x.size(0) == 0:
            return 0.5, 0.5  # 随机猜测基线

        # 获取正确的设备
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            device = next(agent.model_encoder.parameters()).device
        elif hasattr(agent, 'model_ecoder') and agent.model_ecoder is not None:
            device = next(agent.model_ecoder.parameters()).device
        else:
            device = next(agent.model.parameters()).device

        # 设置模型为评估模式
        if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
            agent.model_encoder.eval()
            if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                agent.model_cls.eval()
        else:
            agent.model.eval()

        # 计算损失作为成员推理的特征
        forget_losses = []
        retain_losses = []

        with torch.no_grad():
            # 计算遗忘数据的损失
            for i in range(0, forget_x.size(0), 32):  # 批处理以节省内存
                batch_x = forget_x[i:i+32].to(device)
                batch_y = forget_y[i:i+32].to(device)

                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                losses = F.cross_entropy(logits, batch_y, reduction='none')
                forget_losses.extend(losses.cpu().numpy())

            # 计算保留数据的损失
            for i in range(0, retain_x.size(0), 32):  # 批处理以节省内存
                batch_x = retain_x[i:i+32].to(device)
                batch_y = retain_y[i:i+32].to(device)

                if hasattr(agent, 'model_encoder') and agent.model_encoder is not None:
                    features = agent.model_encoder(batch_x)
                    if hasattr(agent, 'model_cls') and agent.model_cls is not None:
                        logits = agent.model_cls(features)
                    else:
                        logits = features
                else:
                    logits = agent.model(batch_x)

                losses = F.cross_entropy(logits, batch_y, reduction='none')
                retain_losses.extend(losses.cpu().numpy())

        # 构建MIA数据集
        # 标签：1表示成员（保留数据），0表示非成员（遗忘数据）
        mia_features = np.array(forget_losses + retain_losses)
        mia_labels = np.array([0] * len(forget_losses) + [1] * len(retain_losses))

        # 简单的阈值分类器：损失越低，越可能是成员
        threshold = np.median(mia_features)
        mia_predictions = (mia_features < threshold).astype(int)

        # 计算准确率
        mia_accuracy = np.mean(mia_predictions == mia_labels)

        # 计算AUC
        try:
            mia_auc = roc_auc_score(mia_labels, -mia_features)  # 负号因为损失越低越可能是成员
        except:
            mia_auc = 0.5

        return mia_accuracy, mia_auc

    def validate_forgetting_quality(self, agent, forget_task_id: int, test_loaders: List = None) -> Dict[str, Any]:
        """
        验证遗忘质量

        Args:
            agent: 持续学习代理
            forget_task_id: 遗忘的任务ID
            test_loaders: 测试数据加载器列表

        Returns:
            Dict[str, Any]: 验证结果
        """
        results = {}

        # 1. 检查遗忘任务的准确率是否接近随机猜测
        if test_loaders and len(test_loaders) > forget_task_id:
            forget_acc = self._evaluate_test_accuracy(agent, test_loaders[forget_task_id])
            random_acc = 1.0 / 3.0  # 假设3分类

            results['forget_task_accuracy'] = forget_acc
            results['random_accuracy'] = random_acc
            results['accuracy_diff_from_random'] = abs(forget_acc - random_acc)
            results['is_close_to_random'] = results['accuracy_diff_from_random'] < 0.1

            # 2. 检查保留任务的准确率是否保持
            retain_accs = []
            for i, loader in enumerate(test_loaders):
                if i != forget_task_id:
                    acc = self._evaluate_test_accuracy(agent, loader)
                    retain_accs.append(acc)

            if retain_accs:
                results['retain_tasks_accuracy'] = np.mean(retain_accs)
                results['retain_tasks_std'] = np.std(retain_accs)

        # 3. 成员推理攻击评估
        buffer_x, buffer_y, buffer_task_ids = agent.buffer.get_all_data()
        if buffer_x.size(0) > 0:
            forget_mask = (buffer_task_ids == forget_task_id)
            retain_mask = ~forget_mask

            forget_x = buffer_x[forget_mask]
            forget_y = buffer_y[forget_mask]
            retain_x = buffer_x[retain_mask]
            retain_y = buffer_y[retain_mask]

            if forget_x.size(0) > 0 and retain_x.size(0) > 0:
                mia_acc, mia_auc = self.evaluate_membership_inference_attack(
                    agent, forget_x, forget_y, retain_x, retain_y
                )
                results['mia_accuracy'] = mia_acc
                results['mia_auc'] = mia_auc
                results['mia_close_to_random'] = abs(mia_acc - 0.5) < 0.1

        # 4. 整体遗忘质量评估
        quality_checks = []
        if 'is_close_to_random' in results:
            quality_checks.append(results['is_close_to_random'])
        if 'mia_close_to_random' in results:
            quality_checks.append(results['mia_close_to_random'])

        results['overall_forgetting_quality'] = all(quality_checks) if quality_checks else False

        return results
