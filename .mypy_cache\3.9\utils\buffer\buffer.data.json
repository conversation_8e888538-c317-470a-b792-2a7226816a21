{".class": "MypyFile", "_fullname": "utils.buffer.buffer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "utils.buffer.buffer.Buffer", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "utils.buffer.buffer", "mro": ["utils.buffer.buffer.Buffer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model_encoder", "model_classifier", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.__init__", "name": "__init__", "type": null}}, "_ensure_aser_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer._ensure_aser_params", "name": "_ensure_aser_params", "type": null}}, "_retrieve_with_forgetting_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer._retrieve_with_forgetting_filter", "name": "_retrieve_with_forgetting_filter", "type": null}}, "analyze_buffer_distribution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.analyze_buffer_distribution", "name": "analyze_buffer_distribution", "type": null}}, "buffer_tracker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.buffer_tracker", "name": "buffer_tracker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "current_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.current_index", "name": "current_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.device", "name": "device", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forgotten_tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.forgotten_tasks", "name": "forgotten_tasks", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "get_all_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.get_all_data", "name": "get_all_data", "type": null}}, "get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.get_task_data", "name": "get_task_data", "type": null}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.model", "name": "model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.model_classifier", "name": "model_classifier", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.model_encoder", "name": "model_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_seen_so_far": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.n_seen_so_far", "name": "n_seen_so_far", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "optimize_buffer_for_current_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "current_task_id", "max_current_ratio", "min_task_ratio"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.optimize_buffer_for_current_task", "name": "optimize_buffer_for_current_task", "type": null}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.params", "name": "params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "remove_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.remove_task_data", "name": "remove_task_data", "type": null}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.retrieve", "name": "retrieve", "type": null}}, "retrieve_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.retrieve_method", "name": "retrieve_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_forgotten_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "forgotten_tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.set_forgotten_tasks", "name": "set_forgotten_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "forgotten_tasks"], "arg_types": ["utils.buffer.buffer.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_forgotten_tasks of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "x", "y", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.buffer.Buffer.update", "name": "update", "type": null}}, "update_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.buffer.buffer.Buffer.update_method", "name": "update_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "utils.buffer.buffer.Buffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "utils.buffer.buffer.Buffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BufferClassTracker": {".class": "SymbolTableNode", "cross_ref": "utils.buffer.buffer_utils.BufferClassTracker", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.buffer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.buffer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.buffer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.buffer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.buffer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.buffer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "input_size_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.input_size_match", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "n_classes": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.n_classes", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\utils\\buffer\\buffer.py"}