"""
测试简化后的遗忘机制
验证general_main.py中的遗忘功能是否正常工作
"""

import subprocess
import sys
import os


def test_basic_forgetting():
    """测试基本遗忘功能"""
    print("======= 测试基本遗忘功能 =======")
    print("场景：训练3个任务，遗忘任务1")
    
    cmd = [
        sys.executable, "general_main.py",
        "--mode", "train",
        "--agent", "PAER",
        "--data", "seed",
        "--mem_size", "500",
        "--forget_task_ids", "1",
        "--unlearning_epochs", "10",
        "--epoch", "2",
        "--store", "True",
        "--save_path", "./output/test_simplified_forgetting",
        "--verbose", "True"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout[-2000:])  # 显示最后2000个字符
        if result.stderr:
            print("错误输出:")
            print(result.stderr[-1000:])  # 显示最后1000个字符
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("测试超时（10分钟）")
        return False
    except Exception as e:
        print(f"执行失败: {e}")
        return False


def test_multiple_forgetting():
    """测试多任务遗忘功能"""
    print("\n======= 测试多任务遗忘功能 =======")
    print("场景：训练3个任务，遗忘任务0和任务2")
    
    cmd = [
        sys.executable, "general_main.py",
        "--mode", "train",
        "--agent", "PAER",
        "--data", "seed",
        "--mem_size", "500",
        "--forget_task_ids", "0", "2",
        "--unlearning_epochs", "10",
        "--epoch", "2",
        "--store", "True",
        "--save_path", "./output/test_multi_forgetting",
        "--verbose", "True"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout[-2000:])  # 显示最后2000个字符
        if result.stderr:
            print("错误输出:")
            print(result.stderr[-1000:])  # 显示最后1000个字符
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("测试超时（10分钟）")
        return False
    except Exception as e:
        print(f"执行失败: {e}")
        return False


def test_no_forgetting():
    """测试不遗忘的正常训练"""
    print("\n======= 测试正常训练（无遗忘）=======")
    print("场景：正常训练3个任务，不执行遗忘")
    
    cmd = [
        sys.executable, "general_main.py",
        "--mode", "train",
        "--agent", "PAER",
        "--data", "seed",
        "--mem_size", "500",
        "--epoch", "2",
        "--store", "True",
        "--save_path", "./output/test_no_forgetting",
        "--verbose", "True"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout[-2000:])  # 显示最后2000个字符
        if result.stderr:
            print("错误输出:")
            print(result.stderr[-1000:])  # 显示最后1000个字符
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("测试超时（10分钟）")
        return False
    except Exception as e:
        print(f"执行失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试简化后的遗忘机制...")
    print("=" * 50)
    
    # 创建输出目录
    os.makedirs("./output", exist_ok=True)
    
    # 运行测试
    tests = [
        ("正常训练（无遗忘）", test_no_forgetting),
        ("基本遗忘功能", test_basic_forgetting),
        ("多任务遗忘功能", test_multiple_forgetting),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！简化后的遗忘机制工作正常。")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
