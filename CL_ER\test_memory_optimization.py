"""
测试内存优化效果
验证遗忘过程中的内存使用是否得到控制
"""

import torch
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from memory_config import MemoryConfig, get_memory_config, BatchProcessor
from utils.forgetting_scheduler import ForgettingScheduler


def test_memory_config():
    """测试内存配置"""
    print("======= 测试内存配置 =======")
    
    # 测试自动配置
    config = MemoryConfig.auto_config()
    print(f"自动配置完成")
    config.print_memory_usage()
    
    # 测试内存压力检查
    pressure = config.check_memory_pressure()
    print(f"当前内存压力: {'高' if pressure else '正常'}")
    
    # 测试自适应批处理大小
    base_size = 64
    data_size = 1000
    adaptive_size = config.adaptive_batch_size(base_size, data_size)
    print(f"自适应批处理大小: {base_size} -> {adaptive_size}")
    
    return True


def test_batch_processor():
    """测试批处理处理器"""
    print("\n======= 测试批处理处理器 =======")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过批处理测试")
        return True
    
    try:
        # 创建大批量测试数据
        batch_size = 1000
        data_x = torch.randn(batch_size, 1, 1, 310).cuda()
        data_y = torch.randint(0, 3, (batch_size,)).cuda()
        
        print(f"创建测试数据: {data_x.shape}")
        
        # 创建批处理处理器
        processor = BatchProcessor(batch_size=16)
        
        # 定义简单的处理函数
        def simple_process(batch_data):
            if isinstance(batch_data, tuple):
                x, y = batch_data
            else:
                x = batch_data
                y = None
            
            # 模拟一些计算
            result = torch.sum(x, dim=(2, 3))
            return result.cpu()  # 返回到CPU以节省GPU内存
        
        # 分批处理
        print("开始分批处理...")
        results = processor.process_batches((data_x, data_y), simple_process)
        
        print(f"处理完成，共 {len(results)} 个批次")
        
        # 验证结果
        total_samples = sum(r.size(0) for r in results)
        print(f"处理的总样本数: {total_samples}")
        
        return total_samples == batch_size
        
    except Exception as e:
        print(f"批处理测试失败: {e}")
        return False


def test_memory_optimized_evaluation():
    """测试内存优化的评估"""
    print("\n======= 测试内存优化评估 =======")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过评估测试")
        return True
    
    try:
        # 创建模拟代理
        class MockAgent:
            def __init__(self):
                self.model = torch.nn.Sequential(
                    torch.nn.Flatten(),
                    torch.nn.Linear(310, 64),
                    torch.nn.ReLU(),
                    torch.nn.Linear(64, 3)
                ).cuda()
        
        agent = MockAgent()
        
        # 创建大批量测试数据
        data_size = 991  # 模拟实际遗忘数据大小
        x = torch.randn(data_size, 1, 1, 310).cuda()
        y = torch.randint(0, 3, (data_size,)).cuda()
        
        print(f"创建评估数据: {x.shape}")
        
        # 创建遗忘调度器
        scheduler = ForgettingScheduler([1], 3, verbose=True)
        
        print("开始内存优化评估...")
        config = get_memory_config()
        config.print_memory_usage()
        
        # 执行评估
        accuracy = scheduler._evaluate_accuracy(agent, x, y)
        
        print(f"评估完成，准确率: {accuracy:.4f}")
        config.print_memory_usage()
        
        return True
        
    except Exception as e:
        print(f"内存优化评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_pressure_simulation():
    """测试内存压力模拟"""
    print("\n======= 测试内存压力模拟 =======")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过内存压力测试")
        return True
    
    try:
        config = get_memory_config()
        
        print("初始内存状态:")
        config.print_memory_usage()
        
        # 分配大量内存来模拟压力
        tensors = []
        for i in range(10):
            tensor = torch.randn(100, 1000, 1000).cuda()
            tensors.append(tensor)
            
            pressure = config.check_memory_pressure()
            print(f"分配第 {i+1} 个张量后，内存压力: {'高' if pressure else '正常'}")
            
            if pressure:
                print("检测到内存压力，停止分配")
                break
        
        # 清理内存
        del tensors
        config.clear_memory()
        
        print("清理后内存状态:")
        config.print_memory_usage()
        
        return True
        
    except Exception as e:
        print(f"内存压力测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试内存优化...")
    print("=" * 50)
    
    tests = [
        ("内存配置", test_memory_config),
        ("批处理处理器", test_batch_processor),
        ("内存优化评估", test_memory_optimized_evaluation),
        ("内存压力模拟", test_memory_pressure_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！内存优化工作正常。")
        print("\n建议:")
        print("1. 如果仍然遇到内存问题，可以进一步减小批处理大小")
        print("2. 考虑使用梯度累积来保持训练效果")
        print("3. 监控GPU内存使用情况，及时调整配置")
    else:
        print("❌ 部分测试失败，请检查内存优化实现。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
