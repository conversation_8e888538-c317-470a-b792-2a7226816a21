"""
测试设备问题修复
验证CUDA设备不匹配问题是否已解决
"""

import torch
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.buffer.buffer import Buffer
from utils.buffer.reservoir_update import Reservoir_update
from utils.setup_elements import input_size_match
import argparse


def test_buffer_device_consistency():
    """测试缓冲区设备一致性"""
    print("======= 测试缓冲区设备一致性 =======")
    
    # 创建模拟参数
    args = argparse.Namespace()
    args.cuda = torch.cuda.is_available()
    args.mem_size = 100
    args.data = 'seed'
    args.update = 'random'
    args.retrieve = 'random'
    args.buffer_tracker = False
    
    print(f"CUDA可用: {args.cuda}")
    print(f"设备: {'cuda' if args.cuda else 'cpu'}")
    
    try:
        # 创建缓冲区
        buffer = Buffer(None, args)
        print(f"缓冲区创建成功")
        print(f"缓冲区设备: {buffer.buffer_img.device}")
        
        # 创建测试数据
        batch_size = 10
        input_size = input_size_match[args.data]
        
        # 模拟numpy数据（从文件加载的情况）
        x_np = np.random.randn(batch_size, *input_size).astype(np.float32)
        y_np = np.random.randint(0, 3, batch_size).astype(np.int64)
        
        print(f"原始数据类型: x={type(x_np)}, y={type(y_np)}")
        
        # 转换为tensor
        x = torch.from_numpy(x_np).float()
        y = torch.from_numpy(y_np).long()
        
        print(f"转换后数据类型: x={type(x)}, y={type(y)}")
        print(f"转换后设备: x={x.device}, y={y.device}")
        
        # 移动到正确设备
        if args.cuda:
            x = x.cuda()
            y = y.cuda()
            print(f"移动到CUDA后设备: x={x.device}, y={y.device}")
        
        # 测试缓冲区更新
        print("\n测试缓冲区更新...")
        updated_indices = buffer.update(x, y)
        print(f"更新成功，更新了 {len(updated_indices)} 个样本")
        
        # 验证缓冲区数据设备
        print(f"缓冲区数据设备: {buffer.buffer_img.device}")
        print(f"缓冲区标签设备: {buffer.buffer_label.device}")
        
        # 测试数据检索
        print("\n测试数据检索...")
        if buffer.current_index > 0:
            retrieved_x, retrieved_y = buffer.retrieve(size=5)
            print(f"检索成功，检索了 {retrieved_x.size(0)} 个样本")
            print(f"检索数据设备: x={retrieved_x.device}, y={retrieved_y.device}")
        
        print("✓ 缓冲区设备一致性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 缓冲区设备一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_reservoir_update_device():
    """测试Reservoir更新的设备一致性"""
    print("\n======= 测试Reservoir更新设备一致性 =======")
    
    args = argparse.Namespace()
    args.cuda = torch.cuda.is_available()
    args.mem_size = 50
    args.data = 'seed'
    args.update = 'random'
    args.retrieve = 'random'
    args.buffer_tracker = False
    
    try:
        # 创建缓冲区和更新器
        buffer = Buffer(None, args)
        reservoir_update = Reservoir_update(args)
        
        # 创建测试数据
        batch_size = 20
        input_size = input_size_match[args.data]
        
        # 模拟CPU数据
        x_cpu = torch.randn(batch_size, *input_size).float()
        y_cpu = torch.randint(0, 3, (batch_size,)).long()
        
        print(f"CPU数据设备: x={x_cpu.device}, y={y_cpu.device}")
        
        # 移动到GPU（如果可用）
        if args.cuda:
            x_gpu = x_cpu.cuda()
            y_gpu = y_cpu.cuda()
            print(f"GPU数据设备: x={x_gpu.device}, y={y_gpu.device}")
            
            # 测试GPU数据更新
            updated_indices = reservoir_update.update(buffer, x_gpu, y_gpu)
            print(f"GPU数据更新成功，更新了 {len(updated_indices)} 个样本")
        else:
            # 测试CPU数据更新
            updated_indices = reservoir_update.update(buffer, x_cpu, y_cpu)
            print(f"CPU数据更新成功，更新了 {len(updated_indices)} 个样本")
        
        print("✓ Reservoir更新设备一致性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ Reservoir更新设备一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mixed_device_scenario():
    """测试混合设备场景（模拟实际错误情况）"""
    print("\n======= 测试混合设备场景 =======")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过混合设备测试")
        return True
    
    args = argparse.Namespace()
    args.cuda = True
    args.mem_size = 30
    args.data = 'seed'
    args.update = 'random'
    args.retrieve = 'random'
    args.buffer_tracker = False
    
    try:
        # 创建GPU缓冲区
        buffer = Buffer(None, args)
        print(f"缓冲区设备: {buffer.buffer_img.device}")
        
        # 创建CPU数据（模拟从numpy加载的情况）
        batch_size = 15
        input_size = input_size_match[args.data]
        x_cpu = torch.randn(batch_size, *input_size).float()  # CPU数据
        y_cpu = torch.randint(0, 3, (batch_size,)).long()    # CPU数据
        
        print(f"输入数据设备: x={x_cpu.device}, y={y_cpu.device}")
        
        # 这应该会自动处理设备转换
        updated_indices = buffer.update(x_cpu, y_cpu)
        print(f"混合设备更新成功，更新了 {len(updated_indices)} 个样本")
        
        # 验证缓冲区中的数据仍在正确设备上
        print(f"更新后缓冲区设备: {buffer.buffer_img.device}")
        
        print("✓ 混合设备场景测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 混合设备场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试设备问题修复...")
    print("=" * 50)
    
    tests = [
        ("缓冲区设备一致性", test_buffer_device_consistency),
        ("Reservoir更新设备一致性", test_reservoir_update_device),
        ("混合设备场景", test_mixed_device_scenario),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！设备问题已修复。")
    else:
        print("❌ 部分测试失败，可能仍有设备问题。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
