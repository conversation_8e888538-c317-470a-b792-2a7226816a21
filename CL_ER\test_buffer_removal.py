"""
测试缓冲区样本移除功能
验证遗忘任务的样本是否正确从缓冲区中移除
"""

import torch
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.forgetting_scheduler import ForgettingScheduler
from utils.buffer.buffer import Buffer
import argparse


def test_buffer_removal():
    """测试缓冲区样本移除功能"""
    print("======= 测试缓冲区样本移除功能 =======")
    
    # 创建模拟参数
    args = argparse.Namespace()
    args.cuda = False  # 使用CPU测试
    args.mem_size = 100
    args.data = 'seed'
    args.update = 'random'
    args.retrieve = 'random'
    args.buffer_tracker = False
    
    # 创建缓冲区
    buffer = Buffer(None, args)
    
    # 创建模拟数据
    batch_size = 30
    feature_dim = (1, 1, 310)  # SEED数据集的特征维度
    
    # 为3个任务创建数据
    task_data = {}
    for task_id in range(3):
        x = torch.randn(batch_size, *feature_dim)
        y = torch.randint(0, 3, (batch_size,))
        task_data[task_id] = (x, y)
    
    # 模拟添加数据到缓冲区
    print("\n1. 添加数据到缓冲区")
    for task_id in range(3):
        x, y = task_data[task_id]
        
        # 手动设置任务ID（模拟实际使用）
        for i in range(x.size(0)):
            if buffer.current_index < buffer.buffer_img.size(0):
                buffer.buffer_img[buffer.current_index] = x[i]
                buffer.buffer_label[buffer.current_index] = y[i]
                buffer.tasks[buffer.current_index] = task_id
                buffer.current_index += 1
        
        print(f"  任务 {task_id}: 添加了 {x.size(0)} 个样本")
    
    print(f"\n缓冲区总样本数: {buffer.current_index}")
    
    # 分析初始分布
    print("\n2. 初始缓冲区分布:")
    for task_id in range(3):
        count = (buffer.tasks[:buffer.current_index] == task_id).sum().item()
        print(f"  任务 {task_id}: {count} 个样本")
    
    # 创建遗忘调度器
    scheduler = ForgettingScheduler([1], 3, verbose=True, remove_from_buffer=True)
    
    # 测试移除任务1的样本
    print("\n3. 移除任务1的样本")
    scheduler._remove_task_from_buffer(buffer, 1)
    
    # 分析移除后的分布
    print("\n4. 移除后缓冲区分布:")
    for task_id in range(3):
        count = (buffer.tasks[:buffer.current_index] == task_id).sum().item()
        print(f"  任务 {task_id}: {count} 个样本")
    
    # 验证移除效果
    task1_count = (buffer.tasks[:buffer.current_index] == 1).sum().item()
    if task1_count == 0:
        print("\n✓ 测试通过：任务1的样本已完全移除")
        return True
    else:
        print(f"\n✗ 测试失败：任务1仍有 {task1_count} 个样本")
        return False


def test_forgetting_with_buffer_removal():
    """测试完整的遗忘流程（包括缓冲区移除）"""
    print("\n======= 测试完整遗忘流程 =======")
    
    # 模拟简单的代理
    class MockAgent:
        def __init__(self):
            self.model = torch.nn.Sequential(
                torch.nn.Flatten(),
                torch.nn.Linear(310, 64),
                torch.nn.ReLU(),
                torch.nn.Linear(64, 3)
            )
            
            # 创建缓冲区
            args = argparse.Namespace()
            args.cuda = False
            args.mem_size = 60
            args.data = 'seed'
            args.update = 'random'
            args.retrieve = 'random'
            args.buffer_tracker = False
            
            self.buffer = Buffer(None, args)
            
            # 添加模拟数据
            for task_id in range(3):
                for i in range(20):  # 每个任务20个样本
                    if self.buffer.current_index < self.buffer.buffer_img.size(0):
                        x = torch.randn(1, 1, 310)
                        y = torch.randint(0, 3, (1,))
                        
                        self.buffer.buffer_img[self.buffer.current_index] = x
                        self.buffer.buffer_label[self.buffer.current_index] = y
                        self.buffer.tasks[self.buffer.current_index] = task_id
                        self.buffer.current_index += 1
    
    agent = MockAgent()
    
    print(f"初始缓冲区样本数: {agent.buffer.current_index}")
    
    # 分析初始分布
    for task_id in range(3):
        count = (agent.buffer.tasks[:agent.buffer.current_index] == task_id).sum().item()
        print(f"  任务 {task_id}: {count} 个样本")
    
    # 创建遗忘调度器（启用缓冲区移除）
    scheduler = ForgettingScheduler([1], 3, verbose=True, remove_from_buffer=True)
    
    # 模拟遗忘过程
    print("\n执行遗忘操作...")
    
    # 获取缓冲区数据
    buffer_x, buffer_y, buffer_task_ids = agent.buffer.get_all_data()
    
    # 分离遗忘和保留数据
    forget_mask = (buffer_task_ids == 1)
    retain_mask = ~forget_mask
    
    forget_x = buffer_x[forget_mask]
    forget_y = buffer_y[forget_mask]
    retain_x = buffer_x[retain_mask]
    retain_y = buffer_y[retain_mask]
    
    print(f"遗忘数据: {forget_x.size(0)} 个样本")
    print(f"保留数据: {retain_x.size(0)} 个样本")
    
    # 执行缓冲区移除
    if scheduler.remove_from_buffer:
        scheduler._remove_task_from_buffer(agent.buffer, 1)
    
    print(f"\n移除后缓冲区样本数: {agent.buffer.current_index}")
    
    # 验证移除效果
    for task_id in range(3):
        count = (agent.buffer.tasks[:agent.buffer.current_index] == task_id).sum().item()
        print(f"  任务 {task_id}: {count} 个样本")
    
    # 检查任务1是否完全移除
    task1_count = (agent.buffer.tasks[:agent.buffer.current_index] == 1).sum().item()
    if task1_count == 0:
        print("\n✓ 完整遗忘流程测试通过：任务1的样本已完全移除")
        return True
    else:
        print(f"\n✗ 完整遗忘流程测试失败：任务1仍有 {task1_count} 个样本")
        return False


def test_disable_buffer_removal():
    """测试禁用缓冲区移除的情况"""
    print("\n======= 测试禁用缓冲区移除 =======")
    
    # 创建遗忘调度器（禁用缓冲区移除）
    scheduler = ForgettingScheduler([1], 3, verbose=True, remove_from_buffer=False)
    
    print(f"缓冲区移除设置: {scheduler.remove_from_buffer}")
    
    if not scheduler.remove_from_buffer:
        print("✓ 禁用缓冲区移除设置正确")
        return True
    else:
        print("✗ 禁用缓冲区移除设置失败")
        return False


def main():
    """主测试函数"""
    print("开始测试缓冲区样本移除功能...")
    print("=" * 50)
    
    tests = [
        ("缓冲区样本移除", test_buffer_removal),
        ("完整遗忘流程", test_forgetting_with_buffer_removal),
        ("禁用缓冲区移除", test_disable_buffer_removal),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！缓冲区移除功能工作正常。")
        print("\n现在重新运行遗忘训练，遗忘效果应该不会被后续训练撤销！")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
