{"data_mtime": 1754286791, "dep_lines": [3, 6, 7, 2, 6, 8, 9, 10, 11, 12, 13, 686, 1, 4, 5, 492, 716, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 20, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "torch.utils.data", "utils.buffer.buffer", "torch.nn", "torch.utils", "agents.base", "continuum.data_utils", "utils.setup_elements", "utils.utils", "agents.exp_replay", "utils.io", "agents.advanced_unlearning", "torch", "numpy", "os", "datetime", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "array", "continuum", "mmap", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy.random", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.return_types", "torch.types", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "typing", "typing_extensions", "utils", "utils.buffer"], "hash": "0e2f5a6f44465d12d71943c5abf69f50b1df7d02", "id": "agents.prototype_anchored_er", "ignore_all": true, "interface_hash": "0705a7e06ec8fd30c186be2baff78342657e3f4f", "mtime": 1754307586, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\prototype_anchored_er.py", "plugin_data": null, "size": 38075, "suppressed": [], "version_id": "1.15.0"}